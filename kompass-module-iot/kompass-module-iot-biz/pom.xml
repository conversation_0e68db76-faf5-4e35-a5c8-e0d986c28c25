<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kompass-module-iot</artifactId>
        <groupId>com.edu.kk</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>kompass-module-iot-biz</artifactId>

    <name>${project.artifactId}</name>
    <description>
        物联网 模块，主要实现 产品管理、设备管理、协议管理等功能。
        <!-- TODO 芋艿：后续补充下 -->
    </description>

    <dependencies>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-iot-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- MQTT -->
        <dependency>
            <groupId>org.eclipse.paho</groupId>
            <artifactId>org.eclipse.paho.client.mqttv3</artifactId>
        </dependency>
    </dependencies>

</project>
