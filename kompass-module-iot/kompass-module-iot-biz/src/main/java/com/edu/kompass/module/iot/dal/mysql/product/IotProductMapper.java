package com.edu.kompass.module.iot.dal.mysql.product;

import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.mybatis.core.mapper.BaseMapperX;
import com.edu.kompass.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.edu.kompass.module.iot.controller.admin.product.vo.IotProductPageReqVO;
import com.edu.kompass.module.iot.dal.dataobject.product.IotProductDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * IoT 产品 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface IotProductMapper extends BaseMapperX<IotProductDO> {

    default PageResult<IotProductDO> selectPage(IotProductPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<IotProductDO>()
                .likeIfPresent(IotProductDO::getName, reqVO.getName())
                .likeIfPresent(IotProductDO::getProductKey, reqVO.getProductKey())
                .orderByDesc(IotProductDO::getId));
    }

    default IotProductDO selectByProductKey(String productKey) {
        return selectOne(new LambdaQueryWrapperX<IotProductDO>().eq(IotProductDO::getProductKey, productKey));
    }

}