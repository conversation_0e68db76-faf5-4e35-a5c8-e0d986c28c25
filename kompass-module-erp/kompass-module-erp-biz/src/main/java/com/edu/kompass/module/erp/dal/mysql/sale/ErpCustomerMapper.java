package com.edu.kompass.module.erp.dal.mysql.sale;

import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.mybatis.core.mapper.BaseMapperX;
import com.edu.kompass.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.edu.kompass.module.erp.controller.admin.sale.vo.customer.ErpCustomerPageReqVO;
import com.edu.kompass.module.erp.dal.dataobject.sale.ErpCustomerDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * ERP 客户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ErpCustomerMapper extends BaseMapperX<ErpCustomerDO> {

    default PageResult<ErpCustomerDO> selectPage(ErpCustomerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ErpCustomerDO>()
                .likeIfPresent(ErpCustomerDO::getName, reqVO.getName())
                .eqIfPresent(ErpCustomerDO::getMobile, reqVO.getMobile())
                .eqIfPresent(ErpCustomerDO::getTelephone, reqVO.getTelephone())
                .orderByDesc(ErpCustomerDO::getId));
    }

    default List<ErpCustomerDO> selectListByStatus(Integer status) {
        return selectList(ErpCustomerDO::getStatus, status);
    }

}
