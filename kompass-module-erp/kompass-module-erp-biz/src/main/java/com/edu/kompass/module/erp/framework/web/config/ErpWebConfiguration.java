package com.edu.kompass.module.erp.framework.web.config;

import com.edu.kompass.framework.swagger.config.KompassSwaggerAutoConfiguration;
import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * erp 模块的 web 组件的 Configuration
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
public class ErpWebConfiguration {

    /**
     * erp 模块的 API 分组
     */
    @Bean
    public GroupedOpenApi erpGroupedOpenApi() {
        return KompassSwaggerAutoConfiguration.buildGroupedOpenApi("erp");
    }

}
