<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.erp.dal.mysql.statistics.ErpSaleStatisticsMapper">

    <select id="getSalePrice" resultType="java.math.BigDecimal">
        SELECT
            (SELECT IFNULL(SUM(total_price), 0)
             FROM erp_sale_out
             WHERE out_time >= #{beginTime}
               <if test="endTime != null">
                   AND out_time &lt; #{endTime}
               </if>
               AND tenant_id = ${@com.edu.kompass.framework.tenant.core.context.TenantContextHolder@getRequiredTenantId()}
               AND deleted = 0) -
            (SELECT IFNULL(SUM(total_price), 0)
                FROM erp_sale_return
                WHERE return_time >= #{beginTime}
                <if test="endTime != null">
                    AND return_time &lt; #{endTime}
                </if>
                AND tenant_id = ${@com.edu.kompass.framework.tenant.core.context.TenantContextHolder@getRequiredTenantId()}
                AND deleted = 0)
    </select>

</mapper>