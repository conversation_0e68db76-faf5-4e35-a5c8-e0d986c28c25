<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.edu.kk</groupId>
        <artifactId>kompass-module-report</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>kompass-module-report-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        report 模块，主要实现数据可视化报表等功能：
        1. 基于「积木报表」实现，打印设计、报表设计、图形设计、大屏设计等。
    </description>
    <dependencies>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-report-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-test</artifactId>
        </dependency>

        <!-- 积木报表-->
        <dependency>
            <groupId>org.jeecgframework.jimureport</groupId>
            <artifactId>jimureport-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-excel</artifactId>
        </dependency>

    </dependencies>
</project>
