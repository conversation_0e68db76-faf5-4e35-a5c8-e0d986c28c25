<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.edu.kk</groupId>
        <artifactId>kompass-module-crm</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>kompass-module-crm-biz</artifactId>

    <name>${project.artifactId}</name>
    <description>
        crm 包下，客户关系管理（Customer Relationship Management）。
        例如说：客户、联系人、商机、合同、回款等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-crm-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-bpm-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-biz-ip</artifactId>
        </dependency>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-test</artifactId>
        </dependency>
    </dependencies>
</project>
