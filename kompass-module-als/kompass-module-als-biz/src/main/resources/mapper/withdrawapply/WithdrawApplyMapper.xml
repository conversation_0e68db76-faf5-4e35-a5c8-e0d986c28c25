<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.als.dal.mysql.withdrawapply.WithdrawApplyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    
    <select id="statistics" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(amount),0)
        FROM
            als_withdraw_apply
        WHERE
            member_id = #{memberId}
            AND withdraw_status IN
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="reqVO.createTime != null">
                AND create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]}
            </if>
    </select>
</mapper>
