<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.als.dal.mysql.order.OrderMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<!--    <select id="getOrderCount" resultType="java.lang.Integer">-->
<!--        SELECT COUNT(1) FROM als_order-->
<!--        WHERE deleted = 0-->
<!--        <if test="teacherId != null">-->
<!--            AND match_teacher_id = #{teacherId}-->
<!--        </if>-->
<!--        <if test="orderProcessList != null">-->
<!--            AND order_process IN-->
<!--            <foreach item="orderProcess" collection="orderProcessList" separator="," open="(" close=")">-->
<!--                #{orderProcess}-->
<!--            </foreach>-->
<!--        </if>-->
<!--    </select>-->

    <select id="getOrderCountByCustomer" resultType="com.edu.kompass.module.als.controller.admin.order.vo.OrderCountVo">
        SELECT customer_id, COUNT(1) AS orderCount
        FROM als_order
        WHERE deleted = 0
        AND customer_id IN
        <foreach item="customerId" collection="customerIds" separator="," open="(" close=")">
            #{customerId}
        </foreach>
        GROUP BY customer_id
    </select>
    
     <select id="getExtDoListByTeacher" resultType="com.edu.kompass.module.als.dal.dataobject.order.OrderExtDo">
        select a.*,
            c.customer_name,
            c.customer_phone
        from als_order a
        left join als_customer c on c.customer_id = a.customer_id
        left join als_teacher t on t.teacher_id = a.match_teacher_id
        where  a.deleted = 0
            and c.deleted = 0
            and t.deleted = 0
            and t.member_id = #{reqVO.teacherMemberId,jdbcType=BIGINT}
        order by a.order_id desc
    </select>
    
      <select id="getExtDoListByCustomer" resultType="com.edu.kompass.module.als.dal.dataobject.order.OrderExtDo">
        select a.*,
            c.customer_name,
            c.customer_phone,
            t.teacher_name matchTeacher
        from als_order a
        left join als_customer c on c.customer_id = a.customer_id
        left join als_teacher t on t.teacher_id = a.match_teacher_id
        where  a.deleted = 0
            and c.deleted = 0
            and t.deleted = 0
            and c.member_id = #{reqVO.customerMemberId,jdbcType=BIGINT}
        order by a.order_id desc
    </select>
    
    <select id="getOrderCenterList" resultType="com.edu.kompass.module.als.dal.dataobject.order.OrderExtDo">
        select a.*, b.customer_name
        from als_order a
                 left join als_customer b on a.customer_id = b.customer_id
        where a.deleted = 0
          and b.deleted = 0
          and a.order_status = 3
          and a.release_status = 1
          and a.is_suspend = 0
            <if test="reqVO.orderNo != null and reqVO.orderNo != ''">
                AND a.order_no = #{reqVO.orderNo}
            </if>
            <if test="reqVO.orderAreaIdList != null and reqVO.orderAreaIdList.size() > 0">
                AND a.order_area_id IN (
                <foreach collection="reqVO.orderAreaIdList" item="areaId" separator=",">
                    #{areaId}
                </foreach>
                )
            </if>
            <if test="reqVO.needsTagsList != null and reqVO.needsTagsList.size() > 0">
                AND (
                <foreach collection="reqVO.needsTagsList" item="tag" separator=" OR ">
                    JSON_CONTAINS( a.needs_tags,#{tag,jdbcType=VARCHAR})
                </foreach>
                )
            </if>
            <if test="reqVO.kidChr != null and reqVO.kidChr != ''">
                AND a.kid_chr = #{reqVO.kidChr}
            </if>
            <if test="reqVO.kidSex != null and reqVO.kidSex != ''">
                AND (a.kid_sex = #{reqVO.kidSex} or a.kid_sex = 0)
            </if>
            <if test="reqVO.requireSex != null and reqVO.requireSex != ''">
                AND (a.require_sex = #{reqVO.requireSex} or a.require_sex = 0)
            </if>
            <if test="reqVO.timesWeekList != null and reqVO.timesWeekList.size() > 0">
                AND a.times_week IN (
                <foreach collection="reqVO.timesWeekList" item="timesWeek" separator=",">
                    #{timesWeek}
                </foreach>
                )
            </if>
            <if test="reqVO.isOnWeekend != null and reqVO.isOnWeekend != ''">
                AND a.is_on_weekend = #{reqVO.isOnWeekend}
            </if>
            <if test="reqVO.kidStageList != null and reqVO.kidStageList.size() > 0">
                AND a.kid_stage IN (
                <foreach collection="reqVO.kidStageList" item="kidStage" separator=",">
                    #{kidStage}
                </foreach>
                )
            </if>
        order by a.release_time desc
    </select>
    
    <select id="selectExtById" resultType="com.edu.kompass.module.als.dal.dataobject.order.OrderExtDo">
        select a.*,
               c.customer_name,
               c.customer_phone
        from als_order a
                 left join als_customer c on c.customer_id = a.customer_id
        where a.deleted = 0
          and c.deleted = 0
          and a.order_id = #{id}
    </select>
    
    <update id="updateTrackingTime">
        update als_order
        set tracking_time = #{trackingTime},
        actual_tracking_time = #{actualTrackingTime}
        where order_id = #{orderId}
    </update>
</mapper>
