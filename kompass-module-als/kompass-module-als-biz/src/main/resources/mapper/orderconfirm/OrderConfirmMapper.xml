<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.als.dal.mysql.orderconfirm.OrderConfirmMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="pageList" resultType="com.edu.kompass.module.als.dal.dataobject.orderconfirm.OrderConfirmPageDO">
        select
        a.order_confirm_id,
        a.order_id,
        a.customer_id,
        a.teacher_id,
        a.deal_status,
        a.reject_reason_id,
        a.custom_reason,
        a.deal_time,
        a.create_time,
        a.deal_user_id,
        b.customer_name,
        b.customer_phone,
        c.teacher_name,
        c.teacher_phone,
        d.is_suspend
        from als_order_confirm a
                 left join als_customer b on a.customer_id = b.customer_id
                 left join als_teacher c on a.teacher_id = c.teacher_id
                 left join als_order d on a.order_id = d.order_id
        where a.deleted = 0
          and b.deleted = 0
          and c.deleted = 0
          and d.deleted = 0
            <if test="reqVO.orderConfirmId != null">
                AND a.order_confirm_id = #{reqVO.orderConfirmId}
            </if>
            <if test="reqVO.orderId != null">
                AND a.order_id = #{reqVO.orderId}
            </if>
            <if test="reqVO.customerId != null">
                AND a.customer_id = #{reqVO.customerId}
            </if>
            <if test="reqVO.teacherId != null">
                AND a.teacher_id = #{reqVO.teacherId}
            </if>
            <if test="reqVO.dealStatus != null">
                AND a.deal_status = #{reqVO.dealStatus}
            </if>
            <if test="reqVO.rejectReasonId != null">
                AND a.reject_reason_id = #{reqVO.rejectReasonId}
            </if>
            <if test="reqVO.customReason != null and reqVO.customReason != ''">
                AND a.custom_reason LIKE CONCAT('%', #{reqVO.customReason}, '%')
            </if>
            <if test="reqVO.dealTime != null and reqVO.dealTime.length > 0">
                AND a.deal_time BETWEEN #{reqVO.dealTime[0]} AND #{reqVO.dealTime[1]}
            </if>
            <if test="reqVO.dealUserId != null">
                AND a.deal_user_id = #{reqVO.dealUserId}
            </if>
            <if test="reqVO.createTime != null and reqVO.createTime.length > 0">
                AND a.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]}
            </if>
            <if test="reqVO.customerName != null and reqVO.customerName != ''">
                AND b.customer_name LIKE CONCAT('%', #{reqVO.customerName}, '%')
            </if>
            <if test="reqVO.teacherName != null and reqVO.teacherName != ''">
                AND c.teacher_name LIKE CONCAT('%', #{reqVO.teacherName}, '%')
            </if>
            ORDER BY a.order_confirm_id DESC
    </select>

    <select id="OrderConfirmByOrderIds"
            resultType="com.edu.kompass.module.als.controller.admin.orderconfirm.vo.OrderConfirmCountVO">
        select order_id, count(1) count
        from als_order_confirm
        where deleted = 0
          and order_id in
            <foreach item="orderId" collection="orderIds" separator="," open="(" close=")">
                #{orderId}
            </foreach>
        group by order_id
    </select>
    
     <select id="OrderConfirmByOrderIdsAndTeacherId"
            resultType="com.edu.kompass.module.als.controller.admin.orderconfirm.vo.OrderConfirmCountVO">
        select order_id, deal_status
        from als_order_confirm
        where deleted = 0
          and teacher_id = #{teacherId}
          and order_id in
            <foreach item="orderId" collection="orderIds" separator="," open="(" close=")">
                #{orderId}
            </foreach>
          order by order_confirm_id desc
    </select>
</mapper>
