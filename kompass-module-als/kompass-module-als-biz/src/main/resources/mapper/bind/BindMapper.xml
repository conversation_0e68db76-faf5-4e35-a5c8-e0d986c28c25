<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.als.dal.mysql.bind.BindMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->



    <select id="batchQueryBindRecordList" resultType="com.edu.kompass.module.als.dal.dataobject.bind.BindCustomerDo">
        SELECT a.bind_id,
        a.teacher_id,
        a.customer_id,
        b.customer_name,
        a.bind_status,
        a.bind_time
        FROM als_bind a
        LEFT JOIN als_customer b ON a.customer_id = b.customer_id
        WHERE b.customer_id = #{customerId}
        AND a.teacher_id IN
        <foreach collection="teacherIds" item="teacherId" open="(" separator="," close=")">
            #{teacherId}
        </foreach>
        order by a.create_time desc
    </select>
    
    <select id="pageList" resultType="com.edu.kompass.module.als.dal.dataobject.bind.BindDefineDO">
        select b.*,
               c.customer_name,
               c.customer_phone,
               o.order_address,
               o.parse_address
        from als_bind b
                 left join als_customer c on c.customer_id = b.customer_id
                 left join als_order o on o.order_id = b.order_id
        where b.deleted = 0
          and c.deleted = 0
          and b.teacher_id = #{reqVO.teacherId}
          order by b.bind_id desc
    </select>

    <select id="pageTeacherList" resultType="com.edu.kompass.module.als.dal.dataobject.bind.BindTeacherDO">
        select b.*,
               t.teacher_name,
               t.university_name,
               t.profession,
               t.teacher_phone,
               t.teacher_sex,
               t.degree
        from als_bind b
                 left join als_teacher t on t.teacher_id = b.teacher_id
        where b.deleted = 0
          and t.deleted = 0
          and b.customer_id = #{reqVO.customerId}
        order by b.bind_id desc
    </select>
    
    <select id="queryBindTeacherIdList" resultType="java.lang.Long">
        select t.teacher_id
        from als_bind b
                 left join als_teacher t on b.teacher_id = t.teacher_id
        where b.deleted = 0
          and t.deleted = 0
          and b.customer_id = #{customerId,jdbcType=BIGINT}
          and (t.teacher_name like concat('%',  #{keyword,jdbcType=VARCHAR}, '%')
            or t.teacher_phone like concat('%',  #{keyword,jdbcType=VARCHAR}, '%'))
        group by t.teacher_id
    </select>
</mapper>
