<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.als.dal.mysql.depositrefundapply.DepositRefundApplyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    
    <select id="sumApplyRefundDeposit" resultType="java.math.BigDecimal">
        select sum(a.class_hour)
        from als_lesson_hour a
             left join als_deposit_refund_apply b on b.lesson_hour_id = a.lesson_hour_id
        where a.deleted = 0
            and b.deleted = 0
            and b.deal_status in
            <foreach collection="dealStatusList" item="dealStatus" open="(" separator="," close=")">
                #{dealStatus}
            </foreach>
            and a.lesson_hour_id in
            <foreach collection="lessonHourIds" item="lessonHourId" open="(" separator="," close=")">
                #{lessonHourId}
            </foreach>
    </select>
    
    <select id="getDepositPageList" resultType="com.edu.kompass.module.als.controller.app.teacher.vo.DepositHourVO">
        SELECT  a.*,
                c.customer_name
        FROM als_deposit a
        LEFT JOIN als_deposit_refund_apply b on a.deposit_id = b.deposit_id
        LEFT JOIN als_customer c ON a.customer_id = c.customer_id
        where a.deleted = 0
                and (b.deleted = 0 or b.deleted is null)
                and c.deleted = 0
                AND a.teacher_id = #{reqVO.teacherId}
        order by a.deposit_id desc
    </select>
</mapper>
