<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.als.dal.mysql.teacherfav.TeacherFavMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    
    <select id="getOrderCenterFavPage" resultType="com.edu.kompass.module.als.dal.dataobject.order.OrderExtDo">
        select *
        from als_teacher_fav fav
                 inner join als_order o on o.order_id = fav.order_id
        where 
            fav.deleted = 0
          and o.deleted = 0
          and o.is_suspend = 0
          and fav.teacher_id = #{reqVO.teacherId,jdbcType=BIGINT}
          and fav.type = #{reqVO.favType,jdbcType=INTEGER}
          and o.order_status IN
        <foreach collection="reqVO.orderStatusList" item="orderStatus" separator="," open="(" close=")">
            #{orderStatus}
        </foreach>
          and o.release_status IN
        <foreach collection="reqVO.releaseStatusList" item="releaseStatus" separator="," open="(" close=")">
            #{releaseStatus}
        </foreach>
        
        <if test="reqVO.orderNo != null and reqVO.orderNo != ''">
            AND o.order_no = #{reqVO.orderNo}
        </if>
        <if test="reqVO.orderAreaIdList != null and reqVO.orderAreaIdList.size() > 0">
            AND o.order_area_id IN 
            <foreach collection="reqVO.orderAreaIdList" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="reqVO.needsTagsList != null and reqVO.needsTagsList.size() > 0">
            AND 
            <foreach collection="reqVO.needsTagsList" item="tag" separator=" OR " open="(" close=")">
                JSON_CONTAINS( o.needs_tags,#{tag,jdbcType=VARCHAR})
            </foreach>
        </if>
        <if test="reqVO.kidChr != null and reqVO.kidChr != ''">
            AND o.kid_chr = #{reqVO.kidChr}
        </if>
        <if test="reqVO.kidSex != null and reqVO.kidSex != ''">
            AND o.kid_sex = #{reqVO.kidSex}
        </if>
        <if test="reqVO.requireSex != null and reqVO.requireSex != ''">
            AND o.require_sex = #{reqVO.requireSex}
        </if>
        <if test="reqVO.timesWeekList != null and reqVO.timesWeekList.size() > 0">
            AND o.times_week IN 
            <foreach collection="reqVO.timesWeekList" item="timesWeek" separator="," open="(" close=")">
                #{timesWeek}
            </foreach>
        </if>
        <if test="reqVO.isOnWeekend != null and reqVO.isOnWeekend != ''">
            AND o.is_on_weekend = #{reqVO.isOnWeekend}
        </if>
        <if test="reqVO.kidStageList != null and reqVO.kidStageList.size() > 0">
            AND o.kid_stage IN 
            <foreach collection="reqVO.kidStageList" item="kidStage" separator="," open="(" close=")">
                #{kidStage}
            </foreach>
        </if>
        order by fav.teacher_fav_id desc
    </select>
</mapper>
