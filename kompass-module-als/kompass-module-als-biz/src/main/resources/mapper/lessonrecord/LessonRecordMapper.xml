<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.als.dal.mysql.lessonrecord.LessonRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="getLessonRecordByTeacherIds"
            resultType="com.edu.kompass.module.als.dal.dataobject.lessonrecord.LessonRecordDO">
        SELECT
            teacher_id,  MAX(commit_time) commit_time
        FROM als_lesson_record
        WHERE deleted = 0
          AND commit_time is not null
          AND teacher_id IN
        <foreach item="teacherId" collection="teacherIds" separator="," open="(" close=")">
            #{teacherId}
        </foreach>
        GROUP BY teacher_id
    </select>

    <select id="getLastLessonRecordByCustomerIds"
            resultType="com.edu.kompass.module.als.dal.dataobject.lessonrecord.LessonRecordDO">
        SELECT
            customer_id,  MAX(commit_time) commit_time
        FROM als_lesson_record
        WHERE deleted = 0
          AND commit_time is not null
          AND customer_id IN
        <foreach item="customerId" collection="customerIds" separator="," open="(" close=")">
            #{customerId}
        </foreach>
        GROUP BY customer_id
    </select>
    
    <select id="getPageList" resultType="com.edu.kompass.module.als.dal.dataobject.lessonrecord.LessonRecordExtDO">
        select a.*,
               b.customer_name
        from als_lesson_record a
                 left join als_customer b on b.customer_id = a.customer_id
        where a.deleted = 0
          and b.deleted = 0
          <if test="reqVO.teacherId != null">
              and a.teacher_id = #{reqVO.teacherId,jdbcType=BIGINT}
          </if>
          <if test="reqVO.customerId != null">
              and a.customer_id = #{reqVO.customerId,jdbcType=BIGINT}
          </if>
          order by a.lesson_record_id desc
    </select>
</mapper>
