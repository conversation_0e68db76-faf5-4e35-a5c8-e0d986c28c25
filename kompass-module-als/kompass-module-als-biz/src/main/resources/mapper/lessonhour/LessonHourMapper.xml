<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.als.dal.mysql.lessonhour.LessonHourMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

<!--    服务次数、服务课时总数-->
    <select id="calTimesAndHours"
            resultType="com.edu.kompass.module.als.dal.dataobject.lessonrecord.TeacherServiceLessonDo">
        select a.teacher_id, count(1) serviceTimes, SUM(a.class_hour) serviceClassHour
        from (SELECT distinct lesson_record_id, teacher_id, class_hour
              FROM als_lesson_hour
              where deleted = 0
                and teacher_id = #{teacherId ,jdbcType=BIGINT}
                and record_status in
                <foreach collection="recordStatusList" item="recordStatus" open="(" separator="," close=")">
                    #{recordStatus}
                </foreach>
              ) a
        group by a.teacher_id;
    </select>

<!-- 服务过家长数、提交过日志数-->
    <select id="calCustomerRecord"
            resultType="com.edu.kompass.module.als.dal.dataobject.lessonrecord.TeacherServiceCustomerDo">
        select a.teacher_id,a.customer_id, count(1) serviceTimes
        from (SELECT distinct lesson_record_id, teacher_id,customer_id, class_hour
              FROM als_lesson_hour
              where deleted = 0
                and teacher_id = #{teacherId ,jdbcType=BIGINT}
                and record_status in
                <foreach collection="recordStatusList" item="recordStatus" open="(" separator="," close=")">
                    #{recordStatus}
                </foreach>
              ) a
        group by a.customer_id;
    </select>

    <select id="calServiceTimesAndHours"
            resultType="com.edu.kompass.module.als.controller.admin.lessonhour.vo.ServiceLessonVO">
        select a.teacher_id, a.customer_id, count(1) serviceTimes, SUM(a.class_hour) serviceClassHour
        from (SELECT distinct lesson_record_id, teacher_id, customer_id, class_hour
              FROM als_lesson_hour
              where deleted = 0
                and teacher_id = #{teacherId ,jdbcType=BIGINT}
                and record_status in
                <foreach collection="recordStatusList" item="recordStatus" open="(" separator="," close=")">
                    #{recordStatus}
                </foreach>
                and customer_id in
                <foreach collection="customerIds" item="customerId" open="(" separator="," close=")">
                    #{customerId}
                </foreach>
        ) a
        group by a.teacher_id, customer_id;
    </select>
    
    <select id="selectLessonHourCount" resultType="java.lang.Integer">
        select count(1) from (
        select lesson_record_id
        from als_lesson_hour
        where deleted = 0
        <if test="teacherId != null">
            and teacher_id = #{teacherId,jdbcType=BIGINT}
        </if>
        <if test="customerId != null">
            and customer_id = #{customerId,jdbcType=BIGINT}
        </if>
        <if test="recordStatusList != null">
            and record_status in
            <foreach collection="recordStatusList" item="recordStatus" open="(" separator="," close=")">
                #{recordStatus}
            </foreach>
        </if>
        group by lesson_record_id) cc
    </select>
    
    <select id="getLessonDepositHourList" resultType="com.edu.kompass.module.als.dal.dataobject.lessonhour.LessonHourDO">
        select *
        from als_lesson_hour
        where deleted = 0
            <if test="recordStatus != null">
                and record_status = #{recordStatus,jdbcType=INTEGER}
            </if>
            <if test="customerId != null">
                and customer_id = #{customerId,jdbcType=BIGINT}
            </if>
            <if test="teacherId != null">
                and teacher_id = #{teacherId,jdbcType=BIGINT}
            </if>
    </select>
    
    <select id="getRecentLessonHour" resultType="com.edu.kompass.module.als.dal.dataobject.lessonhour.LessonHourDO">
        select *
        from als_lesson_hour
        where deleted = 0
            and customer_id = #{customerId,jdbcType=BIGINT}
            and teacher_id = #{teacherId,jdbcType=BIGINT}
        order by lesson_hour_id desc
        limit #{limit,jdbcType=INTEGER}
    </select>
</mapper>
