<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.als.dal.mysql.lessonplan.LessonPlanMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


    <select id="pageList" resultType="com.edu.kompass.module.als.dal.dataobject.lessonplan.LessonPlanDefineDO">
        SELECT lp.*,
               t.teacher_name,
               c.customer_name
        FROM als_lesson_plan lp
                 LEFT JOIN als_teacher t ON lp.teacher_id = t.teacher_id
                 LEFT JOIN als_customer c ON lp.customer_id = c.customer_id
        where lp.deleted = 0
        <if test="reqVO.orderId != null">
            AND lp.order_id = #{reqVO.orderId,jdbcType=BIGINT}
        </if>
        <if test="reqVO.customerId != null">
            AND lp.customer_id = #{reqVO.customerId}
        </if>
        <if test="reqVO.teacherId != null">
            AND lp.teacher_id = #{reqVO.teacherId}
        </if>
         <if test="reqVO.customerMemberId != null">
            AND c.member_id = #{reqVO.customerMemberId}
        </if>
        <if test="reqVO.teacherMemberId != null">
            AND t.member_id = #{reqVO.teacherMemberId}
        </if>
        <if test="reqVO.planHour != null">
            AND lp.plan_hour >= #{reqVO.planHour}
        </if>
        <if test="reqVO.planAuditStatus != null">
            AND lp.plan_audit_status = #{reqVO.planAuditStatus}
        </if>
        <if test="reqVO.planAuditTime != null">
            AND lp.plan_audit_time BETWEEN #{reqVO.planAuditTime[0]} AND #{reqVO.planAuditTime[1]}
        </if>
        <if test="reqVO.planAuditUserId != null">
            AND lp.plan_audit_user_id = #{reqVO.planAuditUserId}
        </if>
        <if test="reqVO.createTime != null">
            AND lp.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1]}
        </if>
        <if test="reqVO.customerName != null">
            AND c.customer_name like CONCAT('%',#{reqVO.customerName,jdbcType=VARCHAR},'%')
        </if>
        <if test="reqVO.teacherName != null">
            AND t.teacher_name  like CONCAT('%',#{reqVO.teacherName,jdbcType=VARCHAR},'%')
        </if>
        ORDER BY lp.lesson_plan_id DESC
    </select>
    
    <update id="updateFinishedHour">
        UPDATE als_lesson_plan
        SET finished_hour = finished_hour + #{finishedHour, jdbcType=DECIMAL}
        WHERE lesson_plan_id = #{lessonPlanId,jdbcType=BIGINT}
    </update>
</mapper>
