<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.edu.kompass.module.als.dal.mysql.teacherinterview.TeacherInterviewMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    <select id="pageList" resultType="com.edu.kompass.module.als.dal.dataobject.teacherinterview.TeacherInterviewDefineDO">
        SELECT a.*,b.teacher_name,b.teacher_phone,c.nickname interviewerName
        FROM als_teacher_interview a
        left join als_teacher b on a.teacher_id = b.teacher_id
        left join system_users c on a.interviewer = c.id
        <where>
            <if test="reqVO.teacherId != null">
                AND a.teacher_id = #{reqVO.teacherId ,jdbcType=BIGINT}
            </if>
            <if test="reqVO.teacherName != null">
                AND b.teacher_name like concat('%',#{reqVO.teacherName,jdbcType=VARCHAR},'%')
            </if>
            <if test="reqVO.teacherPhone != null">
                AND b.teacher_phone like concat('%',#{reqVO.teacherPhone,jdbcType=VARCHAR},'%')
            </if>
            <if test="reqVO.level != null">
                AND a.level = #{reqVO.level,jdbcType=INTEGER}
            </if>
            <if test="reqVO.interviewer != null">
                AND a.interviewer = #{reqVO.interviewer,jdbcType=BIGINT}
            </if>
            <if test="reqVO.interviewerName != null">
                AND c.nickname like concat('%',#{reqVO.interviewerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="reqVO.interviewTime != null">
                AND a.interview_time BETWEEN #{reqVO.interviewTime[0] } AND #{reqVO.interviewTime[1]}
            </if>
            <if test="reqVO.createTime != null">
                AND a.create_time BETWEEN #{reqVO.createTime[0]} AND #{reqVO.createTime[1] }
            </if>
        </where>
        ORDER BY a.teacher_interview_id DESC
    </select>
</mapper>
