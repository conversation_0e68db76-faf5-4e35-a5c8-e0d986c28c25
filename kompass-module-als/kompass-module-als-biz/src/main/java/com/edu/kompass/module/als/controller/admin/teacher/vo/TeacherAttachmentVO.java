package com.edu.kompass.module.als.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 上传老师附件 Request VO")
@Data
public class TeacherAttachmentVO {

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15326")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "个人照片", requiredMode = Schema.RequiredMode.REQUIRED, example = "个人照片示例")
    private String image1Url;

    @Schema(description = "身份证", requiredMode = Schema.RequiredMode.REQUIRED, example = "身份证示例")
    private String image2Url;

    @Schema(description = "学生证", requiredMode = Schema.RequiredMode.REQUIRED, example = "学生证示例")
    private String image3Url;

    @Schema(description = "毕业证书", requiredMode = Schema.RequiredMode.REQUIRED, example = "毕业证书示例")
    private String image4Url;

    @Schema(description = "学信网学籍", requiredMode = Schema.RequiredMode.REQUIRED, example = "学信网学籍示例")
    private String image5Url;

    @Schema(description = "四级证书", requiredMode = Schema.RequiredMode.REQUIRED, example = "四级证书示例")
    private String image6Url;

    @Schema(description = "六级证书", requiredMode = Schema.RequiredMode.REQUIRED, example = "六级证书示例")
    private String image7Url;

    @Schema(description = "教师资格证", requiredMode = Schema.RequiredMode.REQUIRED, example = "教师资格证示例")
    private String image8Url;

    @Schema(description = "钢琴证书", requiredMode = Schema.RequiredMode.REQUIRED, example = "钢琴证书示例")
    private String image9Url;

    @Schema(description = "合作协议", requiredMode = Schema.RequiredMode.REQUIRED, example = "合作协议示例")
    private String image10Url;

    @Schema(description = "承诺书", requiredMode = Schema.RequiredMode.REQUIRED, example = "承诺书示例")
    private String image11Url;

    @Schema(description = "上岗证", requiredMode = Schema.RequiredMode.REQUIRED, example = "上岗证示例")
    private String image12Url;

    @Schema(description = "雅思证书", requiredMode = Schema.RequiredMode.REQUIRED)
    private String image13Url;

    @Schema(description = "托福证书", requiredMode = Schema.RequiredMode.REQUIRED)
    private String image14Url;

    @Schema(description = "其他", requiredMode = Schema.RequiredMode.REQUIRED, example = "其他示例")
    private String image15Url;

    private Long image1Id;
    private Long image2Id;
    private Long image3Id;
    private Long image4Id;
    private Long image5Id;
    private Long image6Id;
    private Long image7Id;
    private Long image8Id;
    private Long image9Id;
    private Long image10Id;
    private Long image11Id;
    private Long image12Id;
    private Long image13Id;
    private Long image14Id;
    private Long image15Id;

}
