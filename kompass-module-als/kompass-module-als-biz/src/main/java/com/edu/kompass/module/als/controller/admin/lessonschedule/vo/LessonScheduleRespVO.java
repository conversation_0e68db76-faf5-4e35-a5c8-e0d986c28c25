package com.edu.kompass.module.als.controller.admin.lessonschedule.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 时间规划执行 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LessonScheduleRespVO {

    @Schema(description = "时间规划执行ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24106")
    @ExcelProperty("时间规划执行ID")
    private Long lessonScheduleId;

    @Schema(description = "陪学记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20091")
    @ExcelProperty("陪学记录ID")
    private Long lessonRecordId;

    @Schema(description = "开始时间")
    @ExcelProperty("开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @ExcelProperty("结束时间")
    private LocalDateTime endTime;

    @Schema(description = "间隔：分钟", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("间隔：分钟")
    private Integer periodMinute;

    @Schema(description = "实际结束时间")
    @ExcelProperty("实际结束时间")
    private LocalDateTime realEndTime;

    @Schema(description = "实际结束时间误差：分钟", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("实际结束时间误差：分钟")
    private Long errorMinute;

    @Schema(description = "任务名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("任务名称")
    private String taskContent;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("als_schedule_task_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer taskStatus;

    @Schema(description = "原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    @ExcelProperty("原因")
    private String reason;

    @Schema(description = "方案", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("方案")
    private String programme;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
