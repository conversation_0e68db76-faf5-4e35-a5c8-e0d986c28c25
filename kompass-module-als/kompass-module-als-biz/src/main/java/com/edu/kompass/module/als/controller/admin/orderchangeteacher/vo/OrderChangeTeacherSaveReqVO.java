package com.edu.kompass.module.als.controller.admin.orderchangeteacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 更换老师新增/修改 Request VO")
@Data
public class OrderChangeTeacherSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "23180")
    private Long orderChangeTeacherId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11212")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23761")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2067")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "被换原因标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "被换原因标签不能为空")
    private Integer reasonTags;

    @Schema(description = "具体原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不香")
    @NotEmpty(message = "具体原因不能为空")
    private String reason;

    @Schema(description = "对老师-好评", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "对老师-好评不能为空")
    private String goodComment;

    @Schema(description = "对老师-差评", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "对老师-差评不能为空")
    private String badComment;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "10115")
    @NotNull(message = "审核人不能为空")
    private Long auditUserId;

    @Schema(description = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @NotEmpty(message = "审核备注不能为空")
    private String auditRemark;

}
