package com.edu.kompass.module.als.controller.admin.bind.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 绑定 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BindRespVO {

    @Schema(description = "绑定表ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14249")
    private Long bindId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17477")
    private Long customerId;

    @Schema(description = "家长姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String customerName;

    @Schema(description = "家长手机", requiredMode = Schema.RequiredMode.REQUIRED, example = "1730987654")
    private String customerPhone;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27313")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String teacherName;

    @Schema(description = "老师手机", requiredMode = Schema.RequiredMode.REQUIRED, example = "1730987654")
    private String teacherPhone;

    @Schema(description = "绑定人", requiredMode = Schema.RequiredMode.REQUIRED, example = "29426")
    private Long bindUserId;

    @Schema(description = "解绑申请时间")
    private LocalDateTime unbindApplyTime;

    @Schema(description = "当前绑定状态：1已绑定 2已解绑", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer bindStatus;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14383")
    private Long orderId;

    @Schema(description = "绑定时间")
    private LocalDateTime bindTime;

    @Schema(description = "解绑时间")
    private LocalDateTime unbindTime;

    @Schema(description = "解绑人", requiredMode = Schema.RequiredMode.REQUIRED, example = "18073")
    private Long unbindUserId;

    @Schema(description = "解绑原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不对")
    private String unbindReason;

    @Schema(description = "解绑审核状态：0审核中 1审核通过 2驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer unbindAuditStatus;

}
