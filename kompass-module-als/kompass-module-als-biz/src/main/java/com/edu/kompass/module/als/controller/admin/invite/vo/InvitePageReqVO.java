package com.edu.kompass.module.als.controller.admin.invite.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 邀请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InvitePageReqVO extends PageParam {

    @Schema(description = "用户ID", example = "20971")
    private Long memberId;

    @Schema(description = "邀请人ID", example = "19271")
    private Long inviteMemberId;

    @Schema(description = "邀请人类型", example = "1")
    private Integer inviteMemberType;

    @Schema(description = "邀请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] inviteTime;

    @Schema(description = "状态", example = "2")
    private Integer awardStatus;

    @Schema(description = "现金", example = "8228")
    private BigDecimal price;

    @Schema(description = "钱包流水ID", example = "22598")
    private Long walletTransactionId;

    @Schema(description = "课时数")
    private BigDecimal lessonPeriod;

    @Schema(description = "课时包ID", example = "27649")
    private Long customerPackageId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
