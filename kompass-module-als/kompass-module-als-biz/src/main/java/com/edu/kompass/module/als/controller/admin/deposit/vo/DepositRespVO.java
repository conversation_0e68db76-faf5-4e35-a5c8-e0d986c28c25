package com.edu.kompass.module.als.controller.admin.deposit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 课时押金 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DepositRespVO {

    @Schema(description = "押金id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3339")
    @ExcelProperty("押金id")
    private Long depositId;

    @Schema(description = "课时记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "22924")
    @ExcelProperty("课时记录ID")
    private Long lessonHourId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11583")
    @ExcelProperty("家长ID")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20596")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "扣押课时", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("扣押课时")
    private BigDecimal classHour;

    @Schema(description = "押金状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "押金状态", converter = DictConvert.class)
    @DictFormat("als_deposit_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer depositStatus;

    @Schema(description = "押金处理方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "押金处理方式", converter = DictConvert.class)
    @DictFormat("als_deposit_deal_method") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer dealMethod;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED, example = "26744")
    @ExcelProperty("处理人")
    private Long dealUserId;

    @Schema(description = "处理时间")
    @ExcelProperty("处理时间")
    private LocalDateTime dealTime;

    @Schema(description = "家长获得课时-上课时长(h)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("家长获得课时-上课时长(h)")
    private BigDecimal customerGet;

    @Schema(description = "老师获得课时-上课时长(h)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("老师获得课时-上课时长(h)")
    private BigDecimal teacherGet;

    @Schema(description = "平台获得课时-上课时长(h)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("平台获得课时-上课时长(h)")
    private BigDecimal platformGet;

    @Schema(description = "备注时间")
    @ExcelProperty("备注时间")
    private LocalDateTime remarkTime;

    @Schema(description = "备注人", requiredMode = Schema.RequiredMode.REQUIRED, example = "13245")
    @ExcelProperty("备注人")
    private Long remarkUserId;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}