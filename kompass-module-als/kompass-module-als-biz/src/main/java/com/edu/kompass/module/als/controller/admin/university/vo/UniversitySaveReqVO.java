package com.edu.kompass.module.als.controller.admin.university.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 大学信息新增/修改 Request VO")
@Data
public class UniversitySaveReqVO {

    @Schema(description = "大学ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28799")
    private Long universityId;

    @Schema(description = "大学名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String universityName;

    @Schema(description = "标签", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> universityTag;

    @Schema(description = "校徽", requiredMode = Schema.RequiredMode.REQUIRED)
    private String logo;

    @Schema(description = "校徽新地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String logoNew;

    @Schema(description = "所在省份", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "城市", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> location;

}
