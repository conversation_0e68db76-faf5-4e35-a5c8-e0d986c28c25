package com.edu.kompass.module.als.controller.admin.refundapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 家长退款申请新增/修改 Request VO")
@Data
public class RefundApplyUpdateVO {

    @Schema(description = "退款ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28461")
    private Long refundApplyId;

    @Schema(description = "购买记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10453")
    private Long customerPackageId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10409")
    private Long customerId;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal refundAmount;

    @Schema(description = "退款理由选择", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer refundReasonType;

    @Schema(description = "退款理由", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    private String refundReason;

    @Schema(description = "退款状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer refundStatus;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED, example = "24518")
    private Long auditUserId;

    private String auditRemark;

    private LocalDateTime auditTime;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    private String remark;

    @Schema(description = "备注时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime remarkTime;

    @Schema(description = "备注人", requiredMode = Schema.RequiredMode.REQUIRED, example = "26346")
    private Long remarkUserId;

    @Schema(description = "解决方案及复盘", requiredMode = Schema.RequiredMode.REQUIRED)
    private String replay;

}
