package com.edu.kompass.module.als.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 老师类新增/修改 Request VO")
@Data
public class TeacherSaveReqVO {

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15326")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "老师姓名不能为空")
    private String teacherName;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "17302589290")
    @NotEmpty(message = "手机号不能为空")
    private String teacherPhone;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "性别不能为空")
    private Integer teacherSex;

    @Schema(description = "微信号")
    private String wechat;

    @Schema(description = "QQ")
    private String qq;

    @Schema(description = "身份证号")
    private String idNumber;

    @Schema(description = "出生")
    private LocalDateTime birth;

    @Schema(description = "政治面貌", example = "1")
    private Integer politicalStatus;

    @Schema(description = "籍贯", requiredMode = Schema.RequiredMode.REQUIRED, example = "17640")
    @NotNull(message = "籍贯不能为空")
    private Integer nativeAreaId;

    @Schema(description = "接单城市", requiredMode = Schema.RequiredMode.REQUIRED, example = "10656")
    @NotNull(message = "接单城市不能为空")
    private Integer orderCityId;

    @Schema(description = "接单区域", requiredMode = Schema.RequiredMode.REQUIRED, example = "27286")
    private List<Integer> orderAreaId;

    @Schema(description = "大学名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "大学名称不能为空")
    private String universityName;

    @Schema(description = "校区")
    private String campus;

    @Schema(description = "高校城市", requiredMode = Schema.RequiredMode.REQUIRED, example = "5626")
    @NotNull(message = "高校城市不能为空")
    private Integer universityCityId;

    @Schema(description = "在校状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "在校状态不能为空")
    private Integer schoolStatus;

    @Schema(description = "专业", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "专业不能为空")
    private String profession;

    @Schema(description = "学历", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "学历不能为空")
    private Integer degree;

    @Schema(description = "入学年份", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "入学年份不能为空")
    private Integer entryYear;

    @Schema(description = "授课范围", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "授课范围不能为空")
    private List<Integer> teachScope;

    @Schema(description = "时间范围", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "时间范围不能为空")
    private List<Integer> teachTimeRange;

    @Schema(description = "经验值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "经验值不能为空")
    private Integer expValue;

    @Schema(description = "信用值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "信用值不能为空")
    private Integer creditValue;

    @Schema(description = "开始接单日期")
    private LocalDateTime startOrderTime;

    @Schema(description = "有无经验", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "有无经验不能为空")
    private Integer isHaveExperience;

    @Schema(description = "本市现住址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "本市现住址不能为空")
    private String address;

    private Integer addressAreaId;

    /**
     * 经度
     */
    private String longitude;
    /**
     * 纬度
     */
    private String latitude;

    @Schema(description = "可接受单程车程", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "可接受单程车程不能为空")
    private Integer acceptableTime;

    @Schema(description = "注册时间")
    private LocalDateTime registerTime;

    @Schema(description = "来源渠道 ", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "来源渠道 不能为空")
    private Integer teacherChannel;

    @Schema(description = "跟踪时间")
    private LocalDateTime trackingTime;

    @Schema(description = "跟踪备注", example = "你猜")
    private String trackingRemark;

    @Schema(description = "抢单次数")
    private Integer orderTimes;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "是否启用")
    private Integer isEnable;

    @Schema(description = "是否可接单")
    private Integer isAcceptOrder;

    @Schema(description = "身份标签")
    private List<String> idTags;

    @Schema(description = "openId", example = "20845")
    private String openId;

    @Schema(description = "最后服务时间")
    private LocalDateTime lastServiceTime;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastActiveTime;

    @Schema(description = "最近抢单时间")
    private LocalDateTime acceptOrderTime;

    @Schema(description = "运营备注", example = "你猜")
    private String operationRemark;

}
