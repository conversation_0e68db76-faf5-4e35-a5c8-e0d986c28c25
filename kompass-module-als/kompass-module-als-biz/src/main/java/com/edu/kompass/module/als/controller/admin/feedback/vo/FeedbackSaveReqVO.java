package com.edu.kompass.module.als.controller.admin.feedback.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 问题反馈新增/修改 Request VO")
@Data
public class FeedbackSaveReqVO {

    @Schema(description = "反馈ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23964")
    private Long feedbackId;

    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用户类型不能为空")
    private Integer memberType;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8735")
    @NotNull(message = "用户ID不能为空")
    private Long memberId;

    @Schema(description = "反馈类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "反馈类型不能为空")
    private Integer feedbackType;

    @Schema(description = "反馈标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "反馈标题不能为空")
    private String title;

    @Schema(description = "反馈内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "反馈内容不能为空")
    private String content;

    @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "图片地址不能为空")
    private String picUrl;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer feedbackStatus;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "备注不能为空")
    private String remark;

}