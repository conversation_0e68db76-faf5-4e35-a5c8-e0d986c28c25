package com.edu.kompass.module.als.controller.admin.teacher.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 老师绑定类分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherBindPageReqVO extends PageParam {

    @Schema(description = "老师ID", example = "15326")
    private Long teacherId;

    @Schema(description = "老师姓名", example = "王五")
    private String teacherName;

    @Schema(description = "手机号", example = "17302589290")
    private String teacherPhone;

    @Schema(description = "家长ID", example = "15326")
    private Long customerId;
}
