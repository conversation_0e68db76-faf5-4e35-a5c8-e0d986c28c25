package com.edu.kompass.module.als.controller.admin.customerpackage;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.customerpackage.vo.CustomerPackagePageReqVO;
import com.edu.kompass.module.als.controller.admin.customerpackage.vo.CustomerPackageRespVO;
import com.edu.kompass.module.als.controller.admin.customerpackage.vo.CustomerPackageSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.customerpackage.CustomerPackageDO;
import com.edu.kompass.module.als.facade.customer.CustomerFacade;
import com.edu.kompass.module.als.service.customerpackage.CustomerPackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 已购课时包记录")
@RestController
@RequestMapping("/als/customer-package")
@Validated
public class CustomerPackageController {

    @Resource
    private CustomerPackageService customerPackageService;

    @Autowired
    private CustomerFacade customerFacade;

    @PostMapping("/create")
    @Operation(summary = "创建已购课时包记录")
    @PreAuthorize("@ss.hasPermission('als:customer-package:create')")
    public CommonResult<Long> createCustomerPackage(@Valid @RequestBody CustomerPackageSaveReqVO createReqVO) {
        return success(customerPackageService.createCustomerPackage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新已购课时包记录")
    @PreAuthorize("@ss.hasPermission('als:customer-package:update')")
    public CommonResult<Boolean> updateCustomerPackage(@Valid @RequestBody CustomerPackageSaveReqVO updateReqVO) {
        customerPackageService.updateCustomerPackage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除已购课时包记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:customer-package:delete')")
    public CommonResult<Boolean> deleteCustomerPackage(@RequestParam("id") Long id) {
        customerPackageService.deleteCustomerPackage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得已购课时包记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:customer-package:query')")
    public CommonResult<CustomerPackageRespVO> getCustomerPackage(@RequestParam("id") Long id) {
        CustomerPackageDO customerPackage = customerPackageService.getCustomerPackage(id);
        return success(BeanUtils.toBean(customerPackage, CustomerPackageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得已购课时包记录分页")
    @PreAuthorize("@ss.hasPermission('als:customer-package:query')")
    public CommonResult<PageResult<CustomerPackageRespVO>> getCustomerPackagePage(@Valid CustomerPackagePageReqVO pageReqVO) {
        PageResult<CustomerPackageRespVO> customerPackagePage = customerFacade.getCustomerPackagePage(pageReqVO);
        return success(customerPackagePage);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出已购课时包记录 Excel")
    @PreAuthorize("@ss.hasPermission('als:customer-package:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCustomerPackageExcel(@Valid CustomerPackagePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CustomerPackageDO> list = customerPackageService.getCustomerPackagePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "已购课时包记录.xls", "数据", CustomerPackageRespVO.class,
                        BeanUtils.toBean(list, CustomerPackageRespVO.class));
    }

}
