package com.edu.kompass.module.als.facade.order;

import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.module.als.controller.admin.order.vo.*;

import javax.validation.Valid;
import java.util.List;

public interface OrderFacade {

    OrderRespVO getOrder(Long orderId);

    PageResult<OrderRespVO> getOrderPage(OrderPageReqVO pageReqVO);

    void addTeacherOrder(OrderAddTeacherReqVo orderTrackDateReqVo);

    void changeTeacherOrder(OrderChangeTeacherReqVo orderChangeTeacherReqVo);

    void auditOrder(Long orderId);
    void pauseOrder(Long orderId);

    void startOrder(Long orderId);
    void releaseOrder(Long orderId);

    List<OrderMapRespVO> getOrderMapList(OrderPageReqVO pageReqVO);

    void updateLocal(Long id);

    void updateOrder(OrderSaveReqVO updateReqVO);

}
