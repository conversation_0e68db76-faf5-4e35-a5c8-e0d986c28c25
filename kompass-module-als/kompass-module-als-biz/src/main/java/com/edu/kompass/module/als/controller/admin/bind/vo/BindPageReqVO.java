package com.edu.kompass.module.als.controller.admin.bind.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 绑定分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BindPageReqVO extends PageParam {

    @Schema(description = "老师ID", example = "8253")
    private Long teacherId;

    @Schema(description = "家长ID", example = "7145")
    private Long customerId;

    @Schema(description = "绑定人", example = "29426")
    private Long bindUserId;

    @Schema(description = "解绑申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] unbindApplyTime;

    @Schema(description = "当前绑定状态：1已绑定 2已解绑", example = "1")
    private Integer bindStatus;

    @Schema(description = "订单ID", example = "14383")
    private Long orderId;

    @Schema(description = "绑定时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] bindTime;

    @Schema(description = "解绑时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] unbindTime;

    @Schema(description = "解绑人", example = "18073")
    private Long unbindUserId;

    @Schema(description = "解绑原因", example = "不对")
    private String unbindReason;

    @Schema(description = "解绑审核状态：0审核中 1审核通过 2驳回", example = "2")
    private Integer unbindAuditStatus;

}
