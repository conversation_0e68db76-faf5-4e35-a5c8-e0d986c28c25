package com.edu.kompass.module.als.controller.admin.teacheraccount.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 老师账户新增/修改 Request VO")
@Data
public class TeacherAccountSaveReqVO {

    @Schema(description = "账户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19549")
    private Long teacherAccountId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30538")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "余额不能为空")
    private BigDecimal balance;

    @Schema(description = "是否可提现", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否可提现不能为空")
    private Integer isCashOut;

}
