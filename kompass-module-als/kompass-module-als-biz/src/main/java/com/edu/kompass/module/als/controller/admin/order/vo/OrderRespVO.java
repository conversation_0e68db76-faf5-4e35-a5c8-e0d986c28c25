package com.edu.kompass.module.als.controller.admin.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 陪学订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OrderRespVO {

    @Schema(description = "主键", example = "1395")
    private Long orderId;

    @Schema(description = "订单编号", example = "PX20240903")
    private String orderNo;

    @Schema(description = "订单类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @DictFormat("als_order_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer orderType;

    @Schema(description = "课程类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @DictFormat("als_lesson_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer lessonType;

    @Schema(description = "沟通前提条件", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @DictFormat("als_communicate_pre") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> communicatePre;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27437")
    private Long customerId;

    @Schema(description = "家长姓名")
    private String customerName;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "17302589292")
    @ExcelProperty("手机号")
    private String customerPhone;

    @Schema(description = "区域", requiredMode = Schema.RequiredMode.REQUIRED, example = "784")
    private Integer orderAreaId;

    @Schema(description = "区域名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "784")
    private String orderAreaName;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "江苏省南京市XX小区")
    private String orderAddress;

    @Schema(description = "解析地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "江苏省南京市XX小区")
    private String parseAddress;

    @Schema(description = "订单状态", example = "1")
    @DictFormat("als_order_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer orderStatus;

    @Schema(description = "发布状态", example = "1")
    @DictFormat("als_release_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer releaseStatus;

    @Schema(description = "发布人", example = "22980")
    private Long releaseUserId;
    private String releaseUser;

    @Schema(description = "发布时间", example = "2024-07-30 22:52:06")
    private LocalDateTime releaseTime;

    @Schema(description = "进程", example = "1")
    @DictFormat("als_order_process") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer orderProcess;

    @Schema(description = "是否暂停接单", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @DictFormat("als_yes_or_on") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isSuspend;

    @Schema(description = "是否已建群", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @DictFormat("als_yes_or_on") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isGroupChat;

    @Schema(description = "当前负责人ID", example = "1")
    private Long headCurrent;

    @Schema(description = "当前负责人")
    private String headCurrentName;

    @Schema(description = "运营负责人ID", example = "1")
    private Long headOperate;

    @Schema(description = "运营负责人")
    private String headOperateName;

    @Schema(description = "市场负责人ID", example = "1")
    private Long headMarket;

    @Schema(description = "市场负责人")
    private String headMarketName;

    @Schema(description = "家长原需求", example = "1")
    private String orgNeedsContent;

    @Schema(description = "老师性别要求", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("als_require_sex") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer requireSex;

    @Schema(description = "老师能力硬性要求", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("als_hard_require_ability") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> hardRequireAbility;

    @Schema(description = "老师能力要求补充", example = "要求教育专业")
    private String requireAbilityExtra;

    @Schema(description = "需求标签", requiredMode = Schema.RequiredMode.REQUIRED, example = "1,2")
    @DictFormat("als_needs_tags") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> needsTags;

    @Schema(description = "需求侧重点", example = "1,2")
    @DictFormat("als_needs_focus_tags") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> needsFocusTags;

    @Schema(description = "陪学要求", requiredMode = Schema.RequiredMode.REQUIRED, example = "【到家陪学】")
    private String demandContent;

    @Schema(description = "周次", example = "1")
    private Integer timesWeek;

    @Schema(description = "是否是周末订单", example = "1")
    @DictFormat("als_is_on_weekend") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isOnWeekend;

    @Schema(description = "陪学时间范围", requiredMode = Schema.RequiredMode.REQUIRED, example = "1,2")
    @DictFormat("als_time_range") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> timeRange;

    @Schema(description = "跟踪时间", example = "2024-07-30 22:55:45")
    private LocalDateTime trackingTime;

    @Schema(description = "实际跟踪时间", example = "2024-07-30 22:55:49")
    private LocalDateTime actualTrackingTime;

    @Schema(description = "跟踪备注标签", example = "1")
    @DictFormat("als_trace_note") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> trackingRemarkTags;

    @Schema(description = "跟踪备注补充", example = "跟踪备注补充")
    private String orderRemarkExtra;

    @Schema(description = "体验时间", example = "2024-07-30 22:55:59")
    private LocalDateTime expTime;

    @Schema(description = "是否确认体验时间", example = "1")
    @DictFormat("als_yes_or_on") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isConfirmExpTime;

    @Schema(description = "孩子性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("als_sex") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer kidSex;

    @Schema(description = "孩子年级阶段", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    @DictFormat("als_kid_stage") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer kidStage;

    @Schema(description = "沟通结果", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("als_communicate_result") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer communicateResult;

    /**
     * 为什么需要陪学
     */
    private String whyNeed;
    /**
     * 主要教育者
     */
    private String primaryEducator;
    /**
     * 孩子称呼
     */
    private String kidNickName;
    /**
     * 家长与孩子关系
     */
    private String relationship;

    @Schema(description = "学校名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String schoolName;

    @Schema(description = "学校性质", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("als_school_nature") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer schoolNature;

    @Schema(description = "大致排名", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("als_ranking")
    private Integer ranking;

    @Schema(description = "孩子性格", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("als_kid_chr") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String kidChr;

    /**
     * 孩子性格补充
     */
    private String kidChrExtra;
    /**
     * 孩子兴趣
     */
    private String kidInt;
    /**
     * 孩子兴趣补充
     */
    private String kidIntExtra;

    @Schema(description = "邀请人", example = "56")
    private Integer inviterId;


    @Schema(description = "接单数量", example = "1")
    private Integer confirmCount;

    @Schema(description = "抢单老师ID", example = "23378")
    private Long matchTeacherId;

    @Schema(description = "抢单老师", example = "23378")
    private String matchTeacher;

    @Schema(description = "抢单老师手机号", example = "23378")
    private String matchTeacherPhone;

    @Schema(description = "承诺最后服务时间", example = "2024-07-30 22:59:07")
    private LocalDateTime promisedLastServiceTime;

    @Schema(description = "被换老师ID", example = "18313")
    private Long changedTeacherId;

    @Schema(description = "被换老师")
    private String changedTeacher;

    @Schema(description = "被换老师手机号")
    private String changedTeacherPhone;

    @Schema(description = "被换原因标签", example = "1")
    @DictFormat("als_changed_reason_tags") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer changedReasonTags;

    @Schema(description = "是否手工创建", example = "1")
    @DictFormat("als_yes_or_on") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isManualCrate;

    @Schema(description = "订单来源渠道", example = "1")
    @DictFormat("als_source_channel") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer sourceChannel;

    @Schema(description = "注册下单时间", example = "2024-07-30 22:59:23")
    private LocalDateTime registerTime;

    @Schema(description = "订单备注", example = "订单备注")
    private String orderRemark;

    @Schema(description = "活动标签",  example = "1,2")
    @DictFormat("als_active_tags")
    private List<Integer> activeTags;

}
