package com.edu.kompass.module.als.controller.admin.deposit;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

import com.edu.kompass.framework.excel.core.util.ExcelUtils;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.*;

import com.edu.kompass.module.als.controller.admin.deposit.vo.*;
import com.edu.kompass.module.als.dal.dataobject.deposit.DepositDO;
import com.edu.kompass.module.als.service.deposit.DepositService;

@Tag(name = "管理后台 - 课时押金")
@RestController
@RequestMapping("/als/deposit")
@Validated
public class DepositController {

    @Resource
    private DepositService depositService;

    @PostMapping("/create")
    @Operation(summary = "创建课时押金")
    @PreAuthorize("@ss.hasPermission('als:deposit:create')")
    public CommonResult<Long> createDeposit(@Valid @RequestBody DepositSaveReqVO createReqVO) {
        return success(depositService.createDeposit(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课时押金")
    @PreAuthorize("@ss.hasPermission('als:deposit:update')")
    public CommonResult<Boolean> updateDeposit(@Valid @RequestBody DepositSaveReqVO updateReqVO) {
        depositService.updateDeposit(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课时押金")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:deposit:delete')")
    public CommonResult<Boolean> deleteDeposit(@RequestParam("id") Long id) {
        depositService.deleteDeposit(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课时押金")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:deposit:query')")
    public CommonResult<DepositRespVO> getDeposit(@RequestParam("id") Long id) {
        DepositDO deposit = depositService.getDeposit(id);
        return success(BeanUtils.toBean(deposit, DepositRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课时押金分页")
    @PreAuthorize("@ss.hasPermission('als:deposit:query')")
    public CommonResult<PageResult<DepositRespVO>> getDepositPage(@Valid DepositPageReqVO pageReqVO) {
        PageResult<DepositDO> pageResult = depositService.getDepositPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DepositRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课时押金 Excel")
    @PreAuthorize("@ss.hasPermission('als:deposit:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDepositExcel(@Valid DepositPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DepositDO> list = depositService.getDepositPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课时押金.xls", "数据", DepositRespVO.class,
                        BeanUtils.toBean(list, DepositRespVO.class));
    }

}