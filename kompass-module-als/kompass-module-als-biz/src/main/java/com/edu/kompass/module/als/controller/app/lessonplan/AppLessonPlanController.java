package com.edu.kompass.module.als.controller.app.lessonplan;

import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.module.als.controller.app.lessonplan.vo.AppLessonPlanSaveReqVO;
import com.edu.kompass.module.als.facade.app.teacher.AppLessonPlanFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "app - 陪学计划")
@RestController
@RequestMapping("/als/lesson-plan")
@Validated
public class AppLessonPlanController {

    @Resource
    private AppLessonPlanFacade appLessonPlanFacade;

    @PostMapping("/create")
    @Operation(summary = "制定陪学计划")
    public CommonResult<Long> createLessonPlan(@Valid @RequestBody AppLessonPlanSaveReqVO createReqVO) {
        return success(appLessonPlanFacade.createLessonPlan(createReqVO));
    }

}
