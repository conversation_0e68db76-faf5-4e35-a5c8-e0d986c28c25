package com.edu.kompass.module.als.controller.admin.teacherinterview.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Schema(description = "管理后台 - 老师面试新增/修改 Request VO")
@Data
public class TeacherInterviewSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12668")
    private Long teacherInterviewId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2496")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "老师等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "老师等级不能为空")
    private Integer level;

    @Schema(description = "面试官", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "面试官不能为空")
    private Long interviewer;

    @Schema(description = "面试官评价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "面试官评价不能为空")
    private String interviewerEvaluate;

    @Schema(description = "面试时间")
    private Date interviewTime;

    @Schema(description = "师资备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    private String teacherRemark;

    @Schema(description = "基本素质", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "基本素质不能为空")
    private List<Integer> qualityBasic;

    @Schema(description = "综合素质评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "综合素质评分不能为空")
    private List<Integer> qualityComprehensive;

    @Schema(description = "试讲评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "试讲评分不能为空")
    private List<Integer> qualityLecture;

    @Schema(description = "综合评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "综合评分不能为空")
    private BigDecimal finallyScore;

}
