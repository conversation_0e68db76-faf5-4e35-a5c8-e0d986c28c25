package com.edu.kompass.module.als.controller.admin.teacherwithdrawapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 老师提现申请新增/修改 Request VO")
@Data
public class TeacherWithdrawApplySaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19074")
    private Long teacherWithdrawApplyId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1407")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "老师姓名不能为空")
    private String teacherName;

    @Schema(description = "到账金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "到账金额不能为空")
    private BigDecimal toAccountAmount;

    @Schema(description = "手续费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "手续费不能为空")
    private BigDecimal fee;

    @Schema(description = "总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总金额不能为空")
    private BigDecimal totalAmount;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", example = "16527")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "你猜")
    private String auditRemark;

    @Schema(description = "上次等待天数", example = "1")
    private BigDecimal lastWaitDays;
}
