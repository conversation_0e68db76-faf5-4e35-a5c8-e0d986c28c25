package com.edu.kompass.module.als.controller.admin.invite;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.invite.vo.InvitePageReqVO;
import com.edu.kompass.module.als.controller.admin.invite.vo.InviteRespVO;
import com.edu.kompass.module.als.controller.admin.invite.vo.InviteSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.invite.InviteDO;
import com.edu.kompass.module.als.service.invite.InviteService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 邀请")
@RestController
@RequestMapping("/als/invite")
@Validated
public class InviteController {

    @Resource
    private InviteService inviteService;

    @PostMapping("/create")
    @Operation(summary = "创建邀请")
    @PreAuthorize("@ss.hasPermission('als:invite:create')")
    public CommonResult<Long> createInvite(@Valid @RequestBody InviteSaveReqVO createReqVO) {
        return success(inviteService.createInvite(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新邀请")
    @PreAuthorize("@ss.hasPermission('als:invite:update')")
    public CommonResult<Boolean> updateInvite(@Valid @RequestBody InviteSaveReqVO updateReqVO) {
        inviteService.updateInvite(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除邀请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:invite:delete')")
    public CommonResult<Boolean> deleteInvite(@RequestParam("id") Long id) {
        inviteService.deleteInvite(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得邀请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:invite:query')")
    public CommonResult<InviteRespVO> getInvite(@RequestParam("id") Long id) {
        InviteDO invite = inviteService.getInvite(id);
        return success(BeanUtils.toBean(invite, InviteRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得邀请分页")
    @PreAuthorize("@ss.hasPermission('als:invite:query')")
    public CommonResult<PageResult<InviteRespVO>> getInvitePage(@Valid InvitePageReqVO pageReqVO) {
        PageResult<InviteDO> pageResult = inviteService.getInvitePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InviteRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出邀请 Excel")
    @PreAuthorize("@ss.hasPermission('als:invite:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInviteExcel(@Valid InvitePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InviteDO> list = inviteService.getInvitePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "邀请.xls", "数据", InviteRespVO.class,
                        BeanUtils.toBean(list, InviteRespVO.class));
    }

}
