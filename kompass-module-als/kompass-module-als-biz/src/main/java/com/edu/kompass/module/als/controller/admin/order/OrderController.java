package com.edu.kompass.module.als.controller.admin.order;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.pojo.map.AddressInfo;
import com.edu.kompass.framework.common.pojo.map.GeoCodeResponse;
import com.edu.kompass.framework.common.util.map.AddressLocationUtil;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.redis.Redisson.aop.NoRepeatSubmit;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.order.vo.*;
import com.edu.kompass.module.als.dal.dataobject.order.OrderDO;
import com.edu.kompass.module.als.facade.order.OrderFacade;
import com.edu.kompass.module.als.service.order.OrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 陪学订单")
@RestController
@RequestMapping("/als/order")
@Validated
public class OrderController {

    @Resource
    private OrderService orderService;

    @Resource
    private OrderFacade orderFacade;

    @PostMapping("/create")
    @Operation(summary = "创建陪学订单")
    @PreAuthorize("@ss.hasPermission('als:order:create')")
    public CommonResult<Long> createOrder(@Valid @RequestBody OrderSaveReqVO createReqVO) {
        return success(orderService.createOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新陪学订单")
    @PreAuthorize("@ss.hasPermission('als:order:update')")
    public CommonResult<Boolean> updateOrder(@Valid @RequestBody OrderSaveReqVO updateReqVO) {
        orderService.updateOrder(updateReqVO);
        return success(true);
    }

    @PutMapping("/addTrackDate")
    @Operation(summary = "添加跟踪时间")
    @PreAuthorize("@ss.hasPermission('als:order:addTrackDate')")
    public CommonResult<Boolean> addTrackDateOrder(@Valid @RequestBody OrderTrackDateReqVo orderTrackDateReqVo) {
        orderService.addTrackDateOrder(orderTrackDateReqVo);
        return success(true);
    }

    @PutMapping("/addTeacher")
    @Operation(summary = "添加老师")
    @PreAuthorize("@ss.hasPermission('als:order:addTeacher')")
    public CommonResult<Boolean> addTeacher(@Valid @RequestBody OrderAddTeacherReqVo orderTrackDateReqVo) {
        orderFacade.addTeacherOrder(orderTrackDateReqVo);
        return success(true);
    }

    @PutMapping("/changeTeacher")
    @Operation(summary = "更换老师")
    @PreAuthorize("@ss.hasPermission('als:order:changeTeacher')")
    public CommonResult<Boolean> changeTeacher(@Valid @RequestBody OrderChangeTeacherReqVo orderChangeTeacherReqVo) {
        orderFacade.changeTeacherOrder(orderChangeTeacherReqVo);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除陪学订单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:order:delete')")
    public CommonResult<Boolean> deleteOrder(@RequestParam("id") Long id) {
        orderService.deleteOrder(id);
        return success(true);
    }

    @GetMapping("/audit")
    @Operation(summary = "直接通过")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:order:query')")
    public CommonResult<Boolean> audit(@RequestParam("id") Long id) {
        orderFacade.auditOrder(id);
        return success(true);
    }

    @GetMapping("/updateLocal")
    @Operation(summary = "更新位置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:order:query')")
    public CommonResult<Boolean> updateLocal(@RequestParam("id") Long id) {
        orderFacade.updateLocal(id);
        return success(true);
    }

    @GetMapping("/pause")
    @Operation(summary = "暂停接单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:order:query')")
    public CommonResult<Boolean> pause(@RequestParam("id") Long id) {
        orderFacade.pauseOrder(id);
        return success(true);
    }

    @GetMapping("/start")
    @Operation(summary = "开启接单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:order:query')")
    public CommonResult<Boolean> start(@RequestParam("id") Long id) {
        orderFacade.startOrder(id);
        return success(true);
    }

    @GetMapping("/release")
    @Operation(summary = "发布确认")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:order:query')")
    public CommonResult<Boolean> release(@RequestParam("id") Long id) {
        orderFacade.releaseOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得陪学订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:order:query')")
    public CommonResult<OrderRespVO> getOrder(@RequestParam("id") Long id) {
        OrderRespVO order = orderFacade.getOrder(id);
        return success(order);
    }

    @GetMapping("/page")
    @Operation(summary = "体验单分页列表")
    @PreAuthorize("@ss.hasPermission('als:order:query')")
    @NoRepeatSubmit
    public CommonResult<PageResult<OrderRespVO>> getOrderPage(@Valid OrderPageReqVO pageReqVO) {
        PageResult<OrderRespVO> orderPage = orderFacade.getOrderPage(pageReqVO);
        return success(orderPage);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出陪学订单 Excel")
    @PreAuthorize("@ss.hasPermission('als:order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOrderExcel(@Valid OrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OrderDO> list = orderService.getOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "陪学订单.xls", "数据", OrderRespVO.class,
                        BeanUtils.toBean(list, OrderRespVO.class));
    }

    @GetMapping("/map")
    @Operation(summary = "订单地图")
    @PreAuthorize("@ss.hasPermission('als:order:query')")
    @NoRepeatSubmit
    public CommonResult<List<OrderMapRespVO>> getOrderMap(@Valid OrderPageReqVO pageReqVO) {
        List<OrderMapRespVO> orderPage = orderFacade.getOrderMapList(pageReqVO);
        return success(orderPage);
    }

    @GetMapping("/getLL")
    @Operation(summary = "获取经纬度")
    @PermitAll
    public CommonResult<GeoCodeResponse> getLLByAddress(@RequestParam("address") String address) {
        GeoCodeResponse geoCodeResponse = AddressLocationUtil.getLLByAddress(address);
        return success(geoCodeResponse);
    }

    @GetMapping("/getAddress")
    @Operation(summary = "获取详细地址")
    @PermitAll
    public CommonResult<AddressInfo> getAddressByLL(@RequestParam("ll")String ll) {
        AddressInfo addressInfo = AddressLocationUtil.getAddressByLL(ll);
        return success(addressInfo);
    }

    @GetMapping("/getResidentialArea")
    @Operation(summary = "获取小区名字")
    @PermitAll
    public CommonResult<String> getResidentialAreaByLL(@RequestParam("ll")String ll) {
        String residentialArea = AddressLocationUtil.getResidentialArea(ll);
        return success(residentialArea);
    }

    @GetMapping("/getDistance")
    @Operation(summary = "获取两点距离")
    @PermitAll
    public CommonResult<String> getDistance(@RequestParam("form")String from,@RequestParam("to")String to) {
       String distance = AddressLocationUtil.getBetweenDistance(from,to);
        return success(distance);
    }
}
