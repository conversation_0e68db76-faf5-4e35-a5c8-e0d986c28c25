package com.edu.kompass.module.als.controller.admin.university.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 大学信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UniversityPageReqVO extends PageParam {

    @Schema(description = "大学名称", example = "赵六")
    private String universityName;

    @Schema(description = "标签")
    private String universityTag;

    @Schema(description = "校徽")
    private String logo;

    @Schema(description = "校徽新地址")
    private String logoNew;

    @Schema(description = "所在省份")
    private String province;

    @Schema(description = "城市")
    private String location;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
