package com.edu.kompass.module.als.controller.admin.customer.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 家长分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CustomerPageReqVO extends PageParam {

    @Schema(description = "家长ID", example = "2115")
    private Long customerId;

    @Schema(description = "家长姓名", example = "张三")
    private String customerName;

    @Schema(description = "家长性别", example = "1")
    private Integer customerSex;

    @Schema(description = "孩子关系", example = "爸爸")
    private String relationship;

    @Schema(description = "剩余课时", example = "10")
    private BigDecimal lessonPeriodRemain;

    @Schema(description = "手机号")
    private String customerPhone;

    @Schema(description = "openId", example = "6313")
    private String openId;

    @Schema(description = "服务状态", example = "[1,2]")
    private Integer serviceStatus;

    @Schema(description = "获客渠道", example = "1")
    private Integer sourceChannel;

    @Schema(description = "服务标签")
    private String serviceTags;

    @Schema(description = "运营标签")
    private String operationTags;

    @Schema(description = "分级标签")
    private String levelTags;

    @Schema(description = "注册时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] registerTime;

    @Schema(description = "最近登录时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastLoginTime;

    @Schema(description = "运营负责人", example = "7453")
    private Long headOperateUserId;

    @Schema(description = "市场负责人", example = "9044")
    private Long headMarketUserId;

    @Schema(description = "家长备注", example = "你猜")
    private String customerRemark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "更新时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] updateTime;

}
