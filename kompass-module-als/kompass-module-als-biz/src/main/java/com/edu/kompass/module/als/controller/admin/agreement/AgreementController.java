package com.edu.kompass.module.als.controller.admin.agreement;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

import com.edu.kompass.framework.excel.core.util.ExcelUtils;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.*;

import com.edu.kompass.module.als.controller.admin.agreement.vo.*;
import com.edu.kompass.module.als.dal.dataobject.agreement.AgreementDO;
import com.edu.kompass.module.als.service.agreement.AgreementService;

@Tag(name = "管理后台 - 协议")
@RestController
@RequestMapping("/als/agreement")
@Validated
public class AgreementController {

    @Resource
    private AgreementService agreementService;

    @PostMapping("/create")
    @Operation(summary = "创建协议")
    @PreAuthorize("@ss.hasPermission('als:agreement:create')")
    public CommonResult<Long> createAgreement(@Valid @RequestBody AgreementSaveReqVO createReqVO) {
        return success(agreementService.createAgreement(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新协议")
    @PreAuthorize("@ss.hasPermission('als:agreement:update')")
    public CommonResult<Boolean> updateAgreement(@Valid @RequestBody AgreementSaveReqVO updateReqVO) {
        agreementService.updateAgreement(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除协议")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:agreement:delete')")
    public CommonResult<Boolean> deleteAgreement(@RequestParam("id") Long id) {
        agreementService.deleteAgreement(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得协议")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:agreement:query')")
    public CommonResult<AgreementRespVO> getAgreement(@RequestParam("id") Long id) {
        AgreementDO agreement = agreementService.getAgreement(id);
        return success(BeanUtils.toBean(agreement, AgreementRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得协议分页")
    @PreAuthorize("@ss.hasPermission('als:agreement:query')")
    public CommonResult<PageResult<AgreementRespVO>> getAgreementPage(@Valid AgreementPageReqVO pageReqVO) {
        PageResult<AgreementDO> pageResult = agreementService.getAgreementPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AgreementRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出协议 Excel")
    @PreAuthorize("@ss.hasPermission('als:agreement:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAgreementExcel(@Valid AgreementPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgreementDO> list = agreementService.getAgreementPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "协议.xls", "数据", AgreementRespVO.class,
                        BeanUtils.toBean(list, AgreementRespVO.class));
    }

}