package com.edu.kompass.module.als.controller.admin.attachment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 附件新增/修改 Request VO")
@Data
public class AttachmentSaveReqVO {

    @Schema(description = "附件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4036")
    private Long attachmentId;

    @Schema(description = "附件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "附件类型不能为空")
    private Integer attachmentType;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "业务类型不能为空")
    private Integer bizType;

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9916")
    @NotNull(message = "业务ID不能为空")
    private Long bizId;

    @Schema(description = "附件url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "附件url不能为空")
    private String url;

}
