package com.edu.kompass.module.als.controller.admin.customeraddapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 课时添加申请新增/修改 Request VO")
@Data
public class CustomerAddApplySaveReqVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9875")
    private Long customerAddApplyId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8215")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "课时包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21815")
    private Long coursePackageId;

    @Schema(description = "课时包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "课时包名称")
    private String packageName;

    @Schema(description = "课时包类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer packageType;

    @Schema(description = "总课时数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer lessonPeriod;

    @Schema(description = "售价", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private BigDecimal salePrice;

    @Schema(description = "优惠金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private BigDecimal discountAmount;

    @Schema(description = "实付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotNull(message = "实付金额不能为空")
    private BigDecimal actualAmount;

    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "支付方式不能为空")
    private Integer applyMethod;

    @Schema(description = "封存流转金额", example = "100")
    private BigDecimal blockAmount;

    @Schema(description = "资金流转来源", example = "1")
    private Integer directionFrom;

    @Schema(description = "申请理由", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    @NotEmpty(message = "申请理由不能为空")
    private String applyReason;
}
