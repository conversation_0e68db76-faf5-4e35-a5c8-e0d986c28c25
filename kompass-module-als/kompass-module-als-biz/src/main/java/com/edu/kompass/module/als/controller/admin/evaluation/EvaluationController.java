package com.edu.kompass.module.als.controller.admin.evaluation;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.evaluation.vo.EvaluationPageReqVO;
import com.edu.kompass.module.als.controller.admin.evaluation.vo.EvaluationRespVO;
import com.edu.kompass.module.als.controller.admin.evaluation.vo.EvaluationSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.evaluation.EvaluationDO;
import com.edu.kompass.module.als.facade.customer.CustomerEvaluationFacade;
import com.edu.kompass.module.als.service.evaluation.EvaluationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 评价")
@RestController
@RequestMapping("/als/evaluation")
@Validated
public class EvaluationController {

    @Resource
    private EvaluationService evaluationService;
    @Resource
    private CustomerEvaluationFacade customerEvaluationFacade;

    @PostMapping("/create")
    @Operation(summary = "创建评价")
    @PreAuthorize("@ss.hasPermission('als:evaluation:create')")
    public CommonResult<Long> createEvaluation(@Valid @RequestBody EvaluationSaveReqVO createReqVO) {
        return success(evaluationService.createEvaluation(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新评价")
    @PreAuthorize("@ss.hasPermission('als:evaluation:update')")
    public CommonResult<Boolean> updateEvaluation(@Valid @RequestBody EvaluationSaveReqVO updateReqVO) {
        evaluationService.updateEvaluation(updateReqVO);
        return success(true);
    }

    @PutMapping("/deal")
    @Operation(summary = "处理评价")
    @PreAuthorize("@ss.hasPermission('als:evaluation:update')")
    public CommonResult<Boolean> dealEvaluation(@Valid @RequestBody EvaluationSaveReqVO updateReqVO) {
        evaluationService.dealEvaluation(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除评价")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:evaluation:delete')")
    public CommonResult<Boolean> deleteEvaluation(@RequestParam("id") Long id) {
        evaluationService.deleteEvaluation(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得评价")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:evaluation:query')")
    public CommonResult<EvaluationRespVO> getEvaluation(@RequestParam("id") Long id) {
        EvaluationDO evaluation = evaluationService.getEvaluation(id);
        return success(BeanUtils.toBean(evaluation, EvaluationRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "评价分页")
    @PreAuthorize("@ss.hasPermission('als:evaluation:query')")
    public CommonResult<PageResult<EvaluationRespVO>> getEvaluationPage(@Valid EvaluationPageReqVO pageReqVO) {
        PageResult<EvaluationRespVO> evaluationPage = customerEvaluationFacade.getEvaluationPage(pageReqVO);
        return success(evaluationPage);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出评价 Excel")
    @PreAuthorize("@ss.hasPermission('als:evaluation:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportEvaluationExcel(@Valid EvaluationPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EvaluationDO> list = evaluationService.getEvaluationPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "评价.xls", "数据", EvaluationRespVO.class,
                        BeanUtils.toBean(list, EvaluationRespVO.class));
    }

}
