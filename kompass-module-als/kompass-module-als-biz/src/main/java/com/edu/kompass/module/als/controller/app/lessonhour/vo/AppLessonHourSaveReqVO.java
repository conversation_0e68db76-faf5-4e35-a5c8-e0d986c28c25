package com.edu.kompass.module.als.controller.app.lessonhour.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "app - 课时记录新增/修改 Request VO")
@Data
public class AppLessonHourSaveReqVO {

    @Schema(description = "课时记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30176")
    private Long lessonHourId;

    @Schema(description = "陪学记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32391")
    @NotNull(message = "陪学记录ID不能为空")
    private Long lessonRecordId;

    @Schema(description = "购买套餐ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28056")
    @NotNull(message = "购买套餐ID不能为空")
    private String customerPackageIds;

    @Schema(description = "上课类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "上课类型不能为空")
    private Integer lessonType;

    @Schema(description = "人数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "人数不能为空")
    private Integer childNumber;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17477")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27313")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "上课时长(h) 押金时拆分", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "上课时长(h) 押金时拆分不能为空")
    private BigDecimal timeHour;

    @Schema(description = "倍数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "倍数不能为空")
    private BigDecimal multiple;

    @Schema(description = "计算课时", example = "1")
    private BigDecimal lessonHour;

    @Schema(description = "总课时", example = "1")
    private BigDecimal classHour;

    @Schema(description = "老师-课时单价", example = "31904")
    private BigDecimal teacherPrice;

    @Schema(description = "老师-小时单价", example = "13605")
    private BigDecimal teacherHourPrice;

    @Schema(description = "老师-附加总费用", example = "11")
    private BigDecimal teacherExtraCharge;

    @Schema(description = "老师-总薪资", example = "22")
    private BigDecimal teacherAmount;

    @Schema(description = "家长-课时单价", example = "26922")
    private BigDecimal customerPrice;

    @Schema(description = "家长-扣费金额", example = "22")
    private BigDecimal customerCost;

    @Schema(description = "下次上课时间", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotNull(message = "下次上课时间不能为空")
    private LocalDateTime nextTime;

    @Schema(description = "课时记录状态", example = "2")
    private Integer recordStatus;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", example = "21873")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "随便")
    private String auditRemark;

    @Schema(description = "押金处理方式", example = "1")
    private Integer dealMethod;

    @Schema(description = "处理人", example = "10901")
    private Long dealUserId;

    @Schema(description = "处理时间")
    private LocalDateTime dealTime;

    @Schema(description = "家长获得课时-上课时长(h)", example = "1")
    private BigDecimal customerGet;

    @Schema(description = "老师获得课时-上课时长(h)", example = "11")
    private BigDecimal teacherGet;

    @Schema(description = "平台获得课时-上课时长(h)")
    private BigDecimal platformGet;

    @Schema(description = "备注时间")
    private LocalDateTime remarkTime;

    @Schema(description = "备注人", example = "27852")
    private Long remarkUserId;

    @Schema(description = "备注", example = "随便")
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 最后更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String creator;
    /**
     * 更新者，目前使用 SysUser 的 id 编号
     *
     * 使用 String 类型的原因是，未来可能会存在非数值的情况，留好拓展性。
     */
    private String updater;
    /**
     * 是否删除
     */
    private Boolean deleted;

}
