package com.edu.kompass.module.als.controller.admin.leaveapply.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 请假申请列表 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LeaveApplyRespVO {

    @Schema(description = "请假申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13415")
    @ExcelProperty("请假申请ID")
    private Long leaveApplyId;

    @Schema(description = "课时记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3633")
    @ExcelProperty("课时记录ID")
    private Long orderRecordId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1648")
    @ExcelProperty("订单ID")
    private Long orderId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12836")
    @ExcelProperty("家长ID")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12929")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "陪学阶段", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "陪学阶段", converter = DictConvert.class)
    @DictFormat("als_leave_stage") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer stage;

    @Schema(description = "请假方", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "请假方", converter = DictConvert.class)
    @DictFormat("als_who_leave") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer whoLeave;

    @Schema(description = "原定上课时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("原定上课时间")
    private LocalDateTime planTime;

    @Schema(description = "调整后上课时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("调整后上课时间")
    private LocalDateTime newTime;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "申请备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("申请备注")
    private String applyRemark;

    @Schema(description = "审核状态", example = "2")
    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat("als_audit_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer auditStatus;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", example = "15912")
    @ExcelProperty("审核人")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "你说的对")
    @ExcelProperty("审核备注")
    private String auditRemark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
