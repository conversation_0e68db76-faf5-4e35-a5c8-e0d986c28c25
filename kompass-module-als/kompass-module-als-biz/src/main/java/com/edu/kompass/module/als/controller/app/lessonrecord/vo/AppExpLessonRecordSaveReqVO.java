package com.edu.kompass.module.als.controller.app.lessonrecord.vo;

import com.edu.kompass.module.als.controller.app.teacher.vo.AppLessonScheduleSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "App - 陪学记录新增/修改 Request VO")
@Data
public class AppExpLessonRecordSaveReqVO {
    
    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24106")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "陪学记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31357")
    private Long lessonRecordId;

    @Schema(description = "购买记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18033")
    private String customerPackageIds;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25142")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17468")
    private Long teacherId;
    private Long teacherMemberId;

    @Schema(description = "上课类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer lessonType;

    @Schema(description = "陪学人数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer childNumber;

    @Schema(description = "陪学内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String lessonContent;

    @Schema(description = "陪学记录状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer recordStatus;

    @Schema(description = "填写进度", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer process;

    @Schema(description = "课前准备项打分", requiredMode = Schema.RequiredMode.REQUIRED, example = "[4,5,5]")
    private List<Integer> prepareScore;

    @Schema(description = "课后项打分", requiredMode = Schema.RequiredMode.REQUIRED, example = "[4,5]")
    private List<Integer> summaryScore;

    @Schema(description = "课前准备事项", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Integer> prepareItem;

    @Schema(description = "课后总结事项", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Integer> summaryItem;

    @Schema(description = "薄弱点记录", requiredMode = Schema.RequiredMode.REQUIRED)
    private String weakSpot;

    @Schema(description = "行为表现评价", requiredMode = Schema.RequiredMode.REQUIRED)
    private String showEvaluate;

    @Schema(description = "陪学反思", requiredMode = Schema.RequiredMode.REQUIRED)
    private String reflect;

    @Schema(description = "给家长留言", requiredMode = Schema.RequiredMode.REQUIRED)
    private String leaveWord;

    @Schema(description = "开始打卡时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String startTime;

    @Schema(description = "结束打卡时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String endTime;

    @Schema(description = "陪学时长", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal scheduleHour;

    @Schema(description = "下次上课时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String nextTime;

    @Schema(description = "提交时间")
    private LocalDateTime commitTime;
    
    @Schema(description = "时间规划")
    private List<AppLessonScheduleSaveReqVO> taskList;

}
