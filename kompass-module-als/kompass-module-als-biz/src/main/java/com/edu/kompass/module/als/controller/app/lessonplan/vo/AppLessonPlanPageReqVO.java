package com.edu.kompass.module.als.controller.app.lessonplan.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 陪学计划分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppLessonPlanPageReqVO extends PageParam {

    @Schema(description = "订单ID", example = "3150")
    private Long orderId;

    @Schema(description = "家长ID", example = "3571")
    private Long customerId;
    private Long customerMemberId;

    @Schema(description = "家长姓名", example = "张三")
    private String customerName;

    @Schema(description = "老师ID", example = "13022")
    private Long teacherId;
    private Long teacherMemberId;

    @Schema(description = "老师姓名", example = "张三")
    private String teacherName;

    @Schema(description = "计划课时数")
    private Integer planHour;

    @Schema(description = "审核状态", example = "1")
    private Integer planAuditStatus;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] planAuditTime;

    @Schema(description = "审核人", example = "10338")
    private Long planAuditUserId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
