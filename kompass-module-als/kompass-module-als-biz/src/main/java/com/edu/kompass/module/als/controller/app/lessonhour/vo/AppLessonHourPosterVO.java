package com.edu.kompass.module.als.controller.app.lessonhour.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "app - 课时记录海报 VO")
@Data
public class AppLessonHourPosterVO {

    /**
     * 课时记录ID
     */
    @TableId
    private Long lessonHourId;
    /**
     * 陪学记录ID
     */
    private Long lessonRecordId;
    /**
     * 购买套餐ID
     */
    private Long customerPackageId;
    /**
     * 上课类型
     */
    private Integer lessonType;
    /**
     * 人数
     */
    private Integer childNumber;
    /**
     * 家长ID
     */
    private Long customerId;
    /**
     * 老师ID
     */
    private Long teacherId;
    /**
     * 上课时长(h) 押金时拆分
     */
    private BigDecimal timeHour;
    /**
     * 倍数
     */
    private BigDecimal multiple;
    /**
     * 计算课时
     */
    private BigDecimal lessonHour;
    /**
     * 总课时
     */
    private BigDecimal classHour;
    /**
     * 老师-课时单价
     */
    private BigDecimal teacherPrice;
    /**
     * 老师-小时单价(课
     */
    private BigDecimal teacherHourPrice;
    /**
     * 老师-附加总费用
     */
    private BigDecimal teacherExtraCharge;
    /**
     * 老师-总薪资
     */
    private BigDecimal teacherAmount;
    /**
     * 家长-课时单价
     */
    private BigDecimal customerPrice;
    /**
     * 家长-扣费金额
     */
    private BigDecimal customerCost;
    /**
     * 下次上课时间
     */
    private LocalDateTime nextTime;
    /**
     * 课时记录状态
     */
    private Integer recordStatus;
    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;
    /**
     * 审核人
     */
    private Long auditUserId;
    /**
     * 审核备注
     */
    private String auditRemark;
    /**
     * 押金处理方式
     *
     * @see com.edu.kompass.module.als.enums.DepositRefundDealStatusEnum
     */
    private Integer dealMethod;
    /**
     * 处理人
     */
    private Long dealUserId;
    /**
     * 处理时间
     */
    private LocalDateTime dealTime;
    /**
     * 家长获得课时-上课时长(h)
     */
    private BigDecimal customerGet;
    /**
     * 老师获得课时-上课时长(h)
     */
    private BigDecimal teacherGet;
    /**
     * 平台获得课时-上课时长(h)
     */
    private BigDecimal platformGet;
    /**
     * 备注时间
     */
    private LocalDateTime remarkTime;
    /**
     * 备注人
     */
    private Long remarkUserId;
    /**
     * 备注
     */
    private String remark;
}
