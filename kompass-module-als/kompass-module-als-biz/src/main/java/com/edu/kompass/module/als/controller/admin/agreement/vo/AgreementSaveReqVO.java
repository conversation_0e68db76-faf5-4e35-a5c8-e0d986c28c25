package com.edu.kompass.module.als.controller.admin.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 协议新增/修改 Request VO")
@Data
public class AgreementSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10647")
    private Long agreementId;

    @Schema(description = "协议唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "协议唯一标识不能为空")
    private String unionKey;

    @Schema(description = "协议标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "协议标题不能为空")
    private String title;

    @Schema(description = "协议内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "协议内容不能为空")
    private String content;

    @Schema(description = "协议版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "协议版本号不能为空")
    private String version;

}
