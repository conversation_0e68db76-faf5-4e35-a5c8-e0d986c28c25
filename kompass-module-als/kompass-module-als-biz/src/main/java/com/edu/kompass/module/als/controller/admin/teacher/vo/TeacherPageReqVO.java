package com.edu.kompass.module.als.controller.admin.teacher.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 老师类分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherPageReqVO extends PageParam {

    @Schema(description = "老师ID", example = "15326")
    private Long teacherId;

    @Schema(description = "老师姓名", example = "王五")
    private String teacherName;

    @Schema(description = "手机号", example = "17302589290")
    private String teacherPhone;

    @Schema(description = "性别", example = "1")
    private Integer teacherSex;

    @Schema(description = "微信号")
    private String wechat;

    @Schema(description = "身份证号")
    private String idNumber;

    @Schema(description = "出生年份")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime[] birth;

    @Schema(description = "政治面貌", example = "1")
    private Integer politicalStatus;

    @Schema(description = "籍贯", example = "17640")
    private List<Integer> nativeAreaId;

    @Schema(description = "接单城市", example = "10656")
    private Integer orderCityId;

    @Schema(description = "接单区域", example = "27286")
    private List<Integer> orderAreaId;

    @Schema(description = "大学名称", example = "王五")
    private String universityName;

    @Schema(description = "高校城市", example = "5626")
    private Integer universityCityId;

    @Schema(description = "在校状态", example = "2")
    private Integer schoolStatus;

    @Schema(description = "专业")
    private String profession;

    @Schema(description = "学历")
    private Integer degree;

    @Schema(description = "入学年份")
    private Integer entryYear;

    @Schema(description = "授课范围")
    private String teachScope;

    @Schema(description = "时间范围")
    private List<Integer> teachTimeRange;

    @Schema(description = "经验值")
    private Integer expValue;

    @Schema(description = "信用值")
    private Integer creditValue;

    @Schema(description = "开始接单日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startOrderTime;

    @Schema(description = "有无经验")
    private Integer isHaveExperience;

    @Schema(description = "本市现住址")
    private String address;

    private Integer addressAreaId;

    @Schema(description = "可接受单程车程")
    private Integer acceptableTime;

    @Schema(description = "注册时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] registerTime;

    @Schema(description = "来源渠道 ")
    private Integer teacherChannel;

    @Schema(description = "跟踪时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] trackingTime;

    @Schema(description = "跟踪备注", example = "你猜")
    private String trackingRemark;

    @Schema(description = "抢单次数")
    private Integer orderTimes;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "是否启用")
    private Integer isEnable;

    @Schema(description = "是否可接单")
    private Integer isAcceptOrder;

    @Schema(description = "身份标签")
    private String idTags;

    @Schema(description = "openId", example = "20845")
    private String openId;

    @Schema(description = "最后服务时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastServiceTime;

    @Schema(description = "最后登录时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastActiveTime;

    @Schema(description = "最近抢单时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] acceptOrderTime;

    @Schema(description = "运营备注", example = "你猜")
    private String operationRemark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
