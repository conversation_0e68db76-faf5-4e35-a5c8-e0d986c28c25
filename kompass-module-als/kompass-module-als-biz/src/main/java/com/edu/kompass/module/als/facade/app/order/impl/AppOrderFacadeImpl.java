package com.edu.kompass.module.als.facade.app.order.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.map.AddressLocationUtil;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.dict.core.DictFrameworkUtils;
import com.edu.kompass.framework.ip.core.Area;
import com.edu.kompass.framework.ip.core.utils.AreaUtils;
import com.edu.kompass.module.als.controller.admin.orderconfirm.vo.OrderConfirmCountVO;
import com.edu.kompass.module.als.controller.admin.teacherfav.vo.TeacherFavSaveReqVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppCustomerReqVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppOrderPageReqVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppOrderRespVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppTeacherFavRespVO;
import com.edu.kompass.module.als.controller.app.teacher.vo.AppTeacherReqVO;
import com.edu.kompass.module.als.dal.dataobject.customer.CustomerDO;
import com.edu.kompass.module.als.dal.dataobject.order.OrderDO;
import com.edu.kompass.module.als.dal.dataobject.order.OrderExtDo;
import com.edu.kompass.module.als.dal.dataobject.orderconfirm.OrderConfirmDO;
import com.edu.kompass.module.als.dal.dataobject.teacher.TeacherDO;
import com.edu.kompass.module.als.dal.dataobject.teacherfav.TeacherFavDO;
import com.edu.kompass.module.als.enums.*;
import com.edu.kompass.module.als.facade.app.order.AppOrderFacade;
import com.edu.kompass.module.als.service.customer.CustomerService;
import com.edu.kompass.module.als.service.order.OrderService;
import com.edu.kompass.module.als.service.orderconfirm.OrderConfirmService;
import com.edu.kompass.module.als.service.teacher.TeacherService;
import com.edu.kompass.module.als.service.teacherfav.TeacherFavService;
import com.edu.kompass.module.infra.api.config.ConfigKeyConstant;
import com.edu.kompass.module.infra.dal.dataobject.config.ConfigDO;
import com.edu.kompass.module.infra.service.config.ConfigService;
import com.edu.kompass.module.system.dal.dataobject.user.AdminUserDO;
import com.edu.kompass.module.system.service.user.AdminUserService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.edu.kompass.framework.common.exception.enums.GlobalErrorCodeConstants.NO_DATA_PERMISSION;
import static com.edu.kompass.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * App端订单服务实现类
 */
@Service
@Slf4j
public class AppOrderFacadeImpl implements AppOrderFacade {
    
    @Resource
    private OrderService orderService;
    
    @Autowired
    private TeacherService teacherService;
    
    @Autowired
    private TeacherFavService teacherFavService;
    
    @Autowired
    private OrderConfirmService orderConfirmService;
    
    @Autowired
    private CustomerService customerService;
    
    @Autowired
    private ConfigService configService;
    @Resource
    private AdminUserService adminUserService;
    
    @Override
    public PageResult<AppOrderRespVO> getTeacherOrderPage(AppOrderPageReqVO pageReqVO) {
        PageResult<OrderExtDo> pageResult = orderService.getTeacherOrderExtPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())){
            return PageResult.empty();
        }
        PageResult<AppOrderRespVO> result = BeanUtils.toBean(pageResult, AppOrderRespVO.class);
        List<AppOrderRespVO> list = result.getList();

        for (AppOrderRespVO appOrderRespVO : list) {
            appOrderRespVO.setNeedsStrTags(DictFrameworkUtils.getDictDataLabelByValueList(DictTypeConstants.ALS_NEEDS_TAGS, appOrderRespVO.getNeedsTags()));
            appOrderRespVO.setKidStageStr(DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_KID_STAGE, appOrderRespVO.getKidStage()));
            if (isHide(appOrderRespVO.getOrderProcess())){
                String customerPhone = appOrderRespVO.getCustomerPhone();
                appOrderRespVO.setCustomerPhone(maskPhoneNumber(customerPhone));
            }
        }
        return result;
    }

    /**
     * 进程未开始时要隐藏家长信息，体验成功后开放
     * 是否需要隐藏家长电话和地址
     */
    private Boolean isHide(Integer orderProcess) {
        List<Integer> hideList = Lists.newArrayList(OrderProcessEnum.EXPERIENCE_SUCCESS.getCode(), OrderProcessEnum.ACCOMPANIMENT_PLAN_COMPLETED.getCode());
        if (hideList.contains(orderProcess)){
            return false;
        }
        return true;
    } 
    

    private String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            return null;
        }

        // 移除所有非数字字符
        String cleaned = phoneNumber.replaceAll("\\D+", "");

        // 验证是否为11位数字
        if (cleaned.length() != 11) {
            throw new IllegalArgumentException("无效的手机号长度");
        }

        // 替换中间四位为星号
        return cleaned.substring(0, 3) + "****" + cleaned.substring(7);
    }

    @Override
    public PageResult<AppOrderRespVO> getCustomerOrderPage(AppOrderPageReqVO pageReqVO) {
        PageResult<OrderExtDo> pageResult = orderService.getCustomerOrderExtPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())){
            return PageResult.empty();
        }
        PageResult<AppOrderRespVO> result = BeanUtils.toBean(pageResult, AppOrderRespVO.class);
        List<AppOrderRespVO> list = result.getList();

        for (AppOrderRespVO appOrderRespVO : list) {
            appOrderRespVO.setNeedsStrTags(DictFrameworkUtils.getDictDataLabelByValueList(DictTypeConstants.ALS_NEEDS_TAGS, appOrderRespVO.getNeedsTags()));
            appOrderRespVO.setKidStageStr(DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_KID_STAGE, appOrderRespVO.getKidStage()));
        }
        return result;
    }

    /**
     * 接单大厅
     * @param pageReqVO
     * @return
     */
    @Override
    public PageResult<AppOrderRespVO> getOrderCenterPage(AppOrderPageReqVO pageReqVO) {
        log.info("接单大厅:{}", JSONUtil.toJsonStr(pageReqVO));
        TeacherDO teacherDO = teacherService.getTeacherByMemberId(pageReqVO.getTeacherMemberId());
        if (!Objects.equals(teacherDO.getIsEnable(), YNEnum.Y.getCode())){
            log.info("接单大厅:{}", "老师未启用",teacherDO.getTeacherName());
            return PageResult.empty();
        }
        PageResult<OrderExtDo> pageResult = orderService.getOrderCenterPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())){
            return PageResult.empty();
        }
        List<Long> orderIds = pageResult.getList().stream().map(OrderExtDo::getOrderId).collect(Collectors.toList());
        
        Map<Long, OrderConfirmCountVO> map = orderConfirmService.getOrderConfirmByOrderIds(orderIds, teacherDO.getTeacherId());
        PageResult<AppOrderRespVO> result = BeanUtils.toBean(pageResult, AppOrderRespVO.class);
        for (AppOrderRespVO orderRespVO : result.getList()) {
            Area area = AreaUtils.getArea(orderRespVO.getOrderAreaId());
            orderRespVO.setOrderAreaName(area.getName());
            OrderConfirmCountVO orderConfirmCountVO = map.get(orderRespVO.getOrderId());
            orderRespVO.setOrderConfirmStatus(Optional.ofNullable(orderConfirmCountVO).map(OrderConfirmCountVO::getDealStatus).orElse(null));
            setDistance(orderRespVO,teacherDO,orderRespVO);
        }
        return result;
    }
    
    /**
     * 查看接单大厅订单详情-老师
     * @param orderId
     * @return
     */
    @Override
    public AppOrderRespVO getOrderCenterDetail(Long orderId,Long teacherMemberId) {
        Long teacherId = teacherService.getTeacherIdByMemberId(teacherMemberId);
        OrderDO order = orderService.getOrder(orderId);
        if (Objects.isNull(order)){
            return null;
        }
        AppOrderRespVO appOrderRespVO = BeanUtils.toBean(order, AppOrderRespVO.class);

        TeacherFavDO teacherIsFav = teacherFavService.getTeacherIsFav(orderId, teacherId, FavTypeEnum.COLLECTION);
        appOrderRespVO.setFav(Objects.nonNull(teacherIsFav));

        OrderConfirmDO applyOrderConfirm = orderConfirmService.getApplyOrderConfirm(orderId, teacherId);
        // 申请状态
        appOrderRespVO.setOrderConfirmStatus(Optional.ofNullable(applyOrderConfirm).map(OrderConfirmDO::getDealStatus).orElse(null));
        appOrderRespVO.setOrderConfirmDate(Optional.ofNullable(applyOrderConfirm).map(OrderConfirmDO::getCreateTime).orElse(null));
        appOrderRespVO.setDealTime(Optional.ofNullable(applyOrderConfirm).map(OrderConfirmDO::getDealTime).orElse(null));

        if (Objects.nonNull(applyOrderConfirm) && applyOrderConfirm.getRejectReasonId() > 0){
            String rejectReason = DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_REJECT_REASON, applyOrderConfirm.getRejectReasonId());
            appOrderRespVO.setRejectReason(rejectReason + applyOrderConfirm.getCustomReason());
        }
       
        // 阶段
        appOrderRespVO.setKidStageStr(DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_KID_STAGE, order.getKidStage()));

        AlsSexEnum kidSexEnum = AlsSexEnum.getEnumByCode(order.getKidSex());
        appOrderRespVO.setKidSexStr(Objects.isNull(kidSexEnum) ? "" : kidSexEnum.getChildAlias());

        AlsSexEnum requireSexEnum = AlsSexEnum.getEnumByCode(order.getRequireSex());
        appOrderRespVO.setRequireSexStr(Objects.isNull(requireSexEnum) ? "" : requireSexEnum.getTeacherAlias());
        
        List<String> hardRequireStrList = DictFrameworkUtils.getDictDataLabelByValueList(DictTypeConstants.ALS_HARD_REQUIRE_ABILITY, appOrderRespVO.getHardRequireAbility());
        appOrderRespVO.setHardRequire(CollUtil.isNotEmpty(hardRequireStrList) ? String.join("、", hardRequireStrList) : "");

        List<String> needsTags = DictFrameworkUtils.getDictDataLabelByValueList(DictTypeConstants.ALS_NEEDS_TAGS, appOrderRespVO.getNeedsFocusTags());
        appOrderRespVO.setNeedsFocusStrTags(CollUtil.isNotEmpty(needsTags) ? "" : String.join("、", needsTags));
        appOrderRespVO.setIsOnWeekendStr(DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_IS_ON_WEEKEND, order.getIsOnWeekend()));

        String ll = appOrderRespVO.getLongitude() + "," + appOrderRespVO.getLatitude();
        String address = AddressLocationUtil.getCityResidentialArea(ll);
        appOrderRespVO.setOrderAddressShow(address);

        ConfigDO configByKey = configService.getConfigByKey(ConfigKeyConstant.APPLYING_ORDER_COUNT);
        appOrderRespVO.setApplyingOrderCount(Integer.parseInt(configByKey.getValue()));
        
        // 异步记录
        fav(orderId,teacherMemberId,FavTypeEnum.VIEWED);

        return appOrderRespVO;
    }

    @Override
    public AppTeacherFavRespVO fav(Long orderId,Long teacherMemberId,FavTypeEnum favType) {
        log.info("记录老师动作：{},orderId：{},teacherMemberId:{}",favType.getDescription(), orderId,teacherMemberId);
        Long teacherId = teacherService.getTeacherIdByMemberId(teacherMemberId);
        TeacherFavDO teacherIsFav = teacherFavService.getTeacherIsFav(orderId, teacherId,FavTypeEnum.COLLECTION);
        if (Objects.isNull(teacherIsFav)){
            TeacherFavSaveReqVO teacherFavSaveReqVO = new TeacherFavSaveReqVO();
            teacherFavSaveReqVO.setOrderId(orderId);
            teacherFavSaveReqVO.setTeacherId(teacherId);
            teacherFavSaveReqVO.setType(favType.getCode());
            // 插入
            Long teacherFavId = teacherFavService.createTeacherFav(teacherFavSaveReqVO);
            AppTeacherFavRespVO result = BeanUtil.toBean(teacherFavSaveReqVO, AppTeacherFavRespVO.class);
            result.setTeacherFavId(teacherFavId);
            return result;
        }
        AppTeacherFavRespVO result = BeanUtil.toBean(teacherIsFav, AppTeacherFavRespVO.class);
        return result;
    }

    @Override
    public void cancelFav(Long orderId, Long teacherMemberId) {
        log.info("老师取消收藏订单,orderId：{},teacherMemberId:{}", orderId,teacherMemberId);
        Long teacherId = teacherService.getTeacherIdByMemberId(teacherMemberId);
        TeacherFavDO teacherIsFav = teacherFavService.getTeacherIsFav(orderId, teacherId,FavTypeEnum.COLLECTION);
        if (Objects.isNull(teacherIsFav)){
            log.info("订单未收藏过，无需取消");
            return; 
        }
        teacherFavService.deleteTeacherFav(teacherIsFav.getTeacherFavId());
        log.info("取消收藏成功：{}",teacherIsFav.getTeacherFavId());
    }

    /**
     * 申请接单
     * @param orderId
     * @param teacherMemberId
     */
    @Override
    public Long applyOrder(Long orderId, Long teacherMemberId) {
        log.info("申请接单,orderId:{},teacherMemberId:{}", orderId, teacherMemberId);
        TeacherDO teacherDO= teacherService.getTeacherByMemberId(teacherMemberId);
        if (Objects.equals(teacherDO.getIsAcceptOrder(), YNEnum.N.getCode())){
            log.info("{}老师未开启接单功能，订单ID{}",teacherDO.getTeacherId(),orderId);
            throw exception(ErrorCodeConstants.TEACHER_NOT_ACCEPT_ORDER);
        }
        Long teacherId = teacherDO.getTeacherId();
        // 查询申请中的数量
        List<OrderConfirmDO> applyingOrder = orderConfirmService.getApplyingOrder(teacherId);
        if (CollUtil.isNotEmpty(applyingOrder)){
            int size = applyingOrder.size();
            ConfigDO configByKey = configService.getConfigByKey(ConfigKeyConstant.APPLYING_ORDER_COUNT);
            if (size >= Integer.parseInt(configByKey.getValue())){
                log.info("{}老师申请接单数量已达上限，订单ID{}",teacherDO.getTeacherId(),orderId);
                throw exception(ErrorCodeConstants.TEACHER_APPLY_ORDER_COUNT_LIMIT, configByKey.getValue());
            }
        }

        OrderDO order = orderService.getOrder(orderId);
        if (Objects.isNull(order)){
            throw exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        Long orderConfirmId = orderConfirmService.applyOrder(orderId, teacherId, order.getCustomerId());
        return orderConfirmId;
    }

    @Override
    public PageResult<AppOrderRespVO> getMyFav(AppOrderPageReqVO pageReqVO) {
        TeacherDO teacherDO= teacherService.getTeacherByMemberId(pageReqVO.getTeacherMemberId());
        if (!Objects.equals(teacherDO.getIsAcceptOrder(), YNEnum.Y.getCode())){
            log.info("{}老师未开启接单功能",teacherDO.getTeacherId());
            return PageResult.empty();
        }
        Long teacherId = teacherDO.getTeacherId();
        pageReqVO.setTeacherId(teacherId);
        PageResult<OrderExtDo> pageResult = teacherFavService.getOrderCenterFavPage(pageReqVO);
        if (CollUtil.isEmpty(pageResult.getList())){
            return PageResult.empty();
        }
        PageResult<AppOrderRespVO> result = BeanUtils.toBean(pageResult, AppOrderRespVO.class);
        for (AppOrderRespVO orderRespVO : result.getList()) {
            Area area = AreaUtils.getArea(orderRespVO.getOrderAreaId());
            orderRespVO.setOrderAreaName(area.getName());
            setDistance(orderRespVO,teacherDO,orderRespVO);
        }
        return result;
    }

    private void setDistance(AppOrderRespVO orderRespV,TeacherDO teacherDO,AppOrderRespVO orderRespVO){
        if (StrUtil.isAllNotBlank(teacherDO.getLatitude(), teacherDO.getLongitude(),teacherDO.getLatitude(),teacherDO.getLatitude())) {
            orderRespV.setDistance(AddressLocationUtil.calculateDistance(
                    Double.parseDouble(teacherDO.getLatitude()),
                    Double.parseDouble(teacherDO.getLongitude()),
                    Double.parseDouble(orderRespVO.getLatitude()),
                    Double.parseDouble(orderRespVO.getLongitude())));
        }
    }

    @Override
    public AppOrderRespVO getOrder(AppCustomerReqVO reqVO) {
        OrderDO order = orderService.getOrder(reqVO.getId());
        if (Objects.isNull(order)){
            return null;
        }
        Long customerId = customerService.getCustomerIdByMemberId(reqVO.getCustomerMemberId());
        if (!Objects.equals(customerId, order.getCustomerId())){
            log.info("用户无权限查看此订单");
            throw exception(NO_DATA_PERMISSION);
        }
        AppOrderRespVO appOrderRespVO = BeanUtils.toBean(order, AppOrderRespVO.class);
        if (order.getMatchTeacherId() > 0){
            TeacherDO teacher = teacherService.getTeacher(order.getMatchTeacherId());
            appOrderRespVO.setMatchTeacher(teacher.getTeacherName());
            appOrderRespVO.setMatchTeacherPhone(teacher.getTeacherPhone());
        }
        appOrderRespVO.setKidStageStr(DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_KID_STAGE, appOrderRespVO.getKidStage()));
        appOrderRespVO.setNeedsStrTags(DictFrameworkUtils.getDictDataLabelByValueList(DictTypeConstants.ALS_NEEDS_TAGS, appOrderRespVO.getNeedsTags()));
        appOrderRespVO.setHardRequireAbilityStr(DictFrameworkUtils.getDictDataLabelByValueList(DictTypeConstants.ALS_HARD_REQUIRE_ABILITY, appOrderRespVO.getHardRequireAbility()));
        appOrderRespVO.setKidSexStr(DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_SEX, appOrderRespVO.getKidSex()));
        appOrderRespVO.setRequireSexStr(DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_SEX, appOrderRespVO.getRequireSex()));
        String area = AreaUtils.format(appOrderRespVO.getOrderAreaId());
        appOrderRespVO.setOrderAreaName(area);

        CustomerDO customer = customerService.getCustomer(order.getCustomerId());
        appOrderRespVO.setCustomerName(Optional.ofNullable(customer).map(CustomerDO::getCustomerName).orElse(""));
        
        return appOrderRespVO;
    }

    @Override
    public AppOrderRespVO getOrder(AppTeacherReqVO reqVO) {
        OrderDO order = orderService.getOrder(reqVO.getId());
        if (Objects.isNull(order)){
            return null;
        }
        Long teacherId = teacherService.getTeacherIdByMemberId(reqVO.getTeacherMemberId());
        if (!Objects.equals(teacherId, order.getMatchTeacherId())){
            log.info("用户无权限查看此订单");
            throw exception(NO_DATA_PERMISSION);
        }
        AppOrderRespVO appOrderRespVO = BeanUtils.toBean(order, AppOrderRespVO.class);
        if (order.getMatchTeacherId() > 0){
            TeacherDO teacher = teacherService.getTeacher(order.getMatchTeacherId());
            appOrderRespVO.setMatchTeacher(teacher.getTeacherName());
            appOrderRespVO.setMatchTeacherPhone(teacher.getTeacherPhone());
        }
        appOrderRespVO.setKidStageStr(DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_KID_STAGE, appOrderRespVO.getKidStage()));
        appOrderRespVO.setNeedsStrTags(DictFrameworkUtils.getDictDataLabelByValueList(DictTypeConstants.ALS_NEEDS_TAGS, appOrderRespVO.getNeedsTags()));
        appOrderRespVO.setHardRequireAbilityStr(DictFrameworkUtils.getDictDataLabelByValueList(DictTypeConstants.ALS_HARD_REQUIRE_ABILITY, appOrderRespVO.getHardRequireAbility()));
        appOrderRespVO.setKidSexStr(DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_SEX, appOrderRespVO.getKidSex()));
        appOrderRespVO.setRequireSexStr(DictFrameworkUtils.getDictDataLabel(DictTypeConstants.ALS_SEX, appOrderRespVO.getRequireSex()));
        String area = AreaUtils.format(appOrderRespVO.getOrderAreaId());
        appOrderRespVO.setOrderAreaName(area);

        CustomerDO customer = customerService.getCustomer(order.getCustomerId());
        appOrderRespVO.setCustomerName(Optional.ofNullable(customer).map(CustomerDO::getCustomerName).orElse(""));

        if (isHide(appOrderRespVO.getOrderProcess())){
            String customerPhone = appOrderRespVO.getCustomerPhone();
            appOrderRespVO.setCustomerPhone(maskPhoneNumber(customerPhone));
        }

        // 订单的运营人员
        AdminUserDO user = adminUserService.getUser(order.getHeadOperate());
        appOrderRespVO.setHeadOperateName(user.getNickname());
        appOrderRespVO.setHeadOperateVxQrCode(user.getVxQrCode());
        return appOrderRespVO;
    }
}
