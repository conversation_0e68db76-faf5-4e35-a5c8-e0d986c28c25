package com.edu.kompass.module.als.controller.admin.orderchangeteacher.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 更换老师分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderChangeTeacherPageReqVO extends PageParam {

    @Schema(description = "订单ID", example = "11212")
    private Long orderId;

    @Schema(description = "老师ID", example = "23761")
    private Long teacherId;

    @Schema(description = "家长ID", example = "2067")
    private Long customerId;

    @Schema(description = "被换原因标签")
    private Integer reasonTags;

    @Schema(description = "具体原因", example = "不香")
    private String reason;

    @Schema(description = "对老师-好评")
    private String goodComment;

    @Schema(description = "对老师-差评")
    private String badComment;

    @Schema(description = "申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] applyTime;

    @Schema(description = "审核状态", example = "1")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "审核人", example = "10115")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "随便")
    private String auditRemark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
