package com.edu.kompass.module.als.controller.admin.evaluation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 评价新增/修改 Request VO")
@Data
public class EvaluationSaveReqVO {

    @Schema(description = "评价ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15337")
    private Long evaluationId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31540")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19050")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long businessId;
    
    @Schema(description = "评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "评分不能为空")
    private Integer score;

    @Schema(description = "评分等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "评分等级不能为空")
    private String level;

    @Schema(description = "模块", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "模块不能为空")
    private Integer module;

    @Schema(description = "评价内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "评价内容不能为空")
    private String content;

    @Schema(description = "处理状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer dealStatus;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED, example = "9910")
    private Long dealUserId;

    @Schema(description = "跟踪备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    private String remark;

    @Schema(description = "备注时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime remarkTime;

}
