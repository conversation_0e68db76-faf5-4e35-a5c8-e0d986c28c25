package com.edu.kompass.module.als.controller.admin.agreement.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 协议 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AgreementRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10647")
    @ExcelProperty("主键ID")
    private Long agreementId;

    @Schema(description = "协议唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("协议唯一标识")
    private String unionKey;

    @Schema(description = "协议标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("协议标题")
    private String title;

    @Schema(description = "协议内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("协议内容")
    private String content;

    @Schema(description = "协议版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("协议版本号")
    private String version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
