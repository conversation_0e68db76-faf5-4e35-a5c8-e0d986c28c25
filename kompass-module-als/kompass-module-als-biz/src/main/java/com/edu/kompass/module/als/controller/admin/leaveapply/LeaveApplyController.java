package com.edu.kompass.module.als.controller.admin.leaveapply;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.leaveapply.vo.LeaveApplyPageReqVO;
import com.edu.kompass.module.als.controller.admin.leaveapply.vo.LeaveApplyRespVO;
import com.edu.kompass.module.als.controller.admin.leaveapply.vo.LeaveApplySaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.leaveapply.LeaveApplyDO;
import com.edu.kompass.module.als.service.leaveapply.LeaveApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 请假申请列表")
@RestController
@RequestMapping("/als/leave-apply")
@Validated
public class LeaveApplyController {

    @Resource
    private LeaveApplyService leaveApplyService;

    @PostMapping("/create")
    @Operation(summary = "创建请假申请列表")
    @PreAuthorize("@ss.hasPermission('als:leave-apply:create')")
    public CommonResult<Long> createLeaveApply(@Valid @RequestBody LeaveApplySaveReqVO createReqVO) {
        return success(leaveApplyService.createLeaveApply(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新请假申请列表")
    @PreAuthorize("@ss.hasPermission('als:leave-apply:update')")
    public CommonResult<Boolean> updateLeaveApply(@Valid @RequestBody LeaveApplySaveReqVO updateReqVO) {
        leaveApplyService.updateLeaveApply(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除请假申请列表")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:leave-apply:delete')")
    public CommonResult<Boolean> deleteLeaveApply(@RequestParam("id") Long id) {
        leaveApplyService.deleteLeaveApply(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得请假申请列表")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:leave-apply:query')")
    public CommonResult<LeaveApplyRespVO> getLeaveApply(@RequestParam("id") Long id) {
        LeaveApplyDO leaveApply = leaveApplyService.getLeaveApply(id);
        return success(BeanUtils.toBean(leaveApply, LeaveApplyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得请假申请列表分页")
    @PreAuthorize("@ss.hasPermission('als:leave-apply:query')")
    public CommonResult<PageResult<LeaveApplyRespVO>> getLeaveApplyPage(@Valid LeaveApplyPageReqVO pageReqVO) {
        PageResult<LeaveApplyDO> pageResult = leaveApplyService.getLeaveApplyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LeaveApplyRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出请假申请列表 Excel")
    @PreAuthorize("@ss.hasPermission('als:leave-apply:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLeaveApplyExcel(@Valid LeaveApplyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LeaveApplyDO> list = leaveApplyService.getLeaveApplyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "请假申请列表.xls", "数据", LeaveApplyRespVO.class,
                        BeanUtils.toBean(list, LeaveApplyRespVO.class));
    }

}
