package com.edu.kompass.module.als.controller.admin.deposit.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.edu.kompass.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课时押金分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DepositPageReqVO extends PageParam {

    @Schema(description = "课时记录ID", example = "22924")
    private Long lessonHourId;

    @Schema(description = "家长ID", example = "11583")
    private Long customerId;

    @Schema(description = "老师ID", example = "20596")
    private Long teacherId;

    @Schema(description = "扣押课时")
    private BigDecimal classHour;

    @Schema(description = "押金状态", example = "2")
    private Integer depositStatus;

    @Schema(description = "押金处理方式")
    private Integer dealMethod;

    @Schema(description = "处理人", example = "26744")
    private Long dealUserId;

    @Schema(description = "处理时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] dealTime;

    @Schema(description = "家长获得课时-上课时长(h)")
    private BigDecimal customerGet;

    @Schema(description = "老师获得课时-上课时长(h)")
    private BigDecimal teacherGet;

    @Schema(description = "平台获得课时-上课时长(h)")
    private BigDecimal platformGet;

    @Schema(description = "备注时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] remarkTime;

    @Schema(description = "备注人", example = "13245")
    private Long remarkUserId;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}