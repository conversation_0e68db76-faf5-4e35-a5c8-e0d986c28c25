package com.edu.kompass.module.als.controller.admin.lessonplan.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Date;

@Schema(description = "管理后台 - 陪学计划新增/修改 Request VO")
@Data
public class LessonPlanSaveReqVO {

    @Schema(description = "陪学计划ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25582")
    private Long lessonPlanId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3150")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3571")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13022")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "计划课时数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "计划课时数不能为空")
    private Integer planHour;

    @Schema(description = "已完成课时数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer finishedHour;

    @Schema(description = "计划开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Date startTime;

    @Schema(description = "计划结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Date endTime;

    @Schema(description = "总体目标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "总体目标不能为空")
    private String planTarget;

    @Schema(description = "家长期望", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "家长期望不能为空")
    private String customerExpect;

    @Schema(description = "Q1阶段目标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "Q1阶段目标不能为空")
    private String stageQ1Target;

    @Schema(description = "Q2阶段目标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "Q2阶段目标不能为空")
    private String stageQ2Target;

    @Schema(description = "Q3阶段目标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "Q3阶段目标不能为空")
    private String stageQ3Target;

    @Schema(description = "Q4阶段目标", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "Q4阶段目标不能为空")
    private String stageQ4Target;

    @Schema(description = "Q1打分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "Q1打分不能为空")
    private Integer stageQ1Score;

    @Schema(description = "Q2打分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "Q2打分不能为空")
    private Integer stageQ2Score;

    @Schema(description = "Q3打分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "Q3打分不能为空")
    private Integer stageQ3Score;

    @Schema(description = "Q4打分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "Q4打分不能为空")
    private Integer stageQ4Score;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "审核状态不能为空")
    private Integer planAuditStatus;

    @Schema(description = "审核时间")
    private LocalDateTime planAuditTime;

    @Schema(description = "审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "10338")
    @NotNull(message = "审核人不能为空")
    private Long planAuditUserId;

    @Schema(description = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "审核备注不能为空")
    private String planAuditRemark;

}
