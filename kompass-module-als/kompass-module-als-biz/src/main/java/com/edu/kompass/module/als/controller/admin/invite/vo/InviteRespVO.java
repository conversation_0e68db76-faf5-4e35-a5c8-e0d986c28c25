package com.edu.kompass.module.als.controller.admin.invite.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 邀请 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InviteRespVO {

    @Schema(description = "邀请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18062")
    @ExcelProperty("邀请ID")
    private Long inviteId;

    @Schema(description = "用户ID", example = "20971")
    @ExcelProperty("用户ID")
    private Long memberId;

    @Schema(description = "邀请人ID", example = "19271")
    @ExcelProperty("邀请人ID")
    private Long inviteMemberId;

    @Schema(description = "邀请人类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "邀请人类型", converter = DictConvert.class)
    @DictFormat("als_invite_member_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer inviteMemberType;

    @Schema(description = "邀请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("邀请时间")
    private LocalDateTime inviteTime;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("als_award_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer awardStatus;

    @Schema(description = "现金", example = "8228")
    @ExcelProperty("现金")
    private BigDecimal price;

    @Schema(description = "钱包流水ID", example = "22598")
    @ExcelProperty("钱包流水ID")
    private Long walletTransactionId;

    @Schema(description = "课时数")
    @ExcelProperty("课时数")
    private BigDecimal lessonPeriod;

    @Schema(description = "课时包ID", example = "27649")
    @ExcelProperty("课时包ID")
    private Long customerPackageId;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
