package com.edu.kompass.module.als.controller.admin.teacheraccount.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 老师账户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TeacherAccountRespVO {

    @Schema(description = "账户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19549")
    @ExcelProperty("账户ID")
    private Long teacherAccountId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30538")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("余额")
    private BigDecimal balance;

    @Schema(description = "是否可提现", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "是否可提现", converter = DictConvert.class)
    @DictFormat("als_yes_or_on") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isCashOut;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
