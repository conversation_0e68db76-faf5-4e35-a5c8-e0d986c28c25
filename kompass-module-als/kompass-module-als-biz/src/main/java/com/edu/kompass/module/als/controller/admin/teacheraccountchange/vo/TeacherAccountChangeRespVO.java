package com.edu.kompass.module.als.controller.admin.teacheraccountchange.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 老师账户变更记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TeacherAccountChangeRespVO {

    @Schema(description = "变更ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18214")
    @ExcelProperty("变更ID")
    private Long teacherAccountChangeId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13993")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("金额")
    private BigDecimal amount;

    @Schema(description = "变更后余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("变更后余额")
    private BigDecimal balance;

    @Schema(description = "变更备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("变更备注")
    private String remark;

    @Schema(description = "变更业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "变更业务类型", converter = DictConvert.class)
    @DictFormat("als_teacher_account_business_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer businessType;

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6262")
    @ExcelProperty("业务ID")
    private Long businessId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
