package com.edu.kompass.module.als.controller.admin.lessonplan;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.audit.AuditVo;
import com.edu.kompass.module.als.controller.admin.lessonplan.vo.AuditReqVO;
import com.edu.kompass.module.als.controller.admin.lessonplan.vo.LessonPlanPageReqVO;
import com.edu.kompass.module.als.controller.admin.lessonplan.vo.LessonPlanRespVO;
import com.edu.kompass.module.als.controller.admin.lessonplan.vo.LessonPlanSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.lessonplan.LessonPlanDO;
import com.edu.kompass.module.als.facade.teacher.LessonPlanFacade;
import com.edu.kompass.module.als.service.lessonplan.LessonPlanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 陪学计划")
@RestController
@RequestMapping("/als/lesson-plan")
@Validated
public class LessonPlanController {

    @Resource
    private LessonPlanService lessonPlanService;

    @Resource
    private LessonPlanFacade lessonPlanFacade;

    @PostMapping("/create")
    @Operation(summary = "创建陪学计划")
    @PreAuthorize("@ss.hasPermission('als:lesson-plan:create')")
    public CommonResult<Long> createLessonPlan(@Valid @RequestBody LessonPlanSaveReqVO createReqVO) {
        return success(lessonPlanService.createLessonPlan(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新陪学计划")
    @PreAuthorize("@ss.hasPermission('als:lesson-plan:update')")
    public CommonResult<Boolean> updateLessonPlan(@Valid @RequestBody LessonPlanSaveReqVO updateReqVO) {
        lessonPlanService.updateLessonPlan(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除陪学计划")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:lesson-plan:delete')")
    public CommonResult<Boolean> deleteLessonPlan(@RequestParam("id") Long id) {
        lessonPlanService.deleteLessonPlan(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得陪学计划")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:lesson-plan:query')")
    public CommonResult<LessonPlanRespVO> getLessonPlan(@RequestParam("id") Long id) {
        LessonPlanDO lessonPlan = lessonPlanService.getLessonPlan(id);
        return success(BeanUtils.toBean(lessonPlan, LessonPlanRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得陪学计划分页")
    @PreAuthorize("@ss.hasPermission('als:lesson-plan:query')")
    public CommonResult<PageResult<LessonPlanRespVO>> getLessonPlanPage(@Valid LessonPlanPageReqVO pageReqVO) {
        PageResult<LessonPlanRespVO> pageResult = lessonPlanFacade.pageList(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出陪学计划 Excel")
    @PreAuthorize("@ss.hasPermission('als:lesson-plan:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonPlanExcel(@Valid LessonPlanPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonPlanDO> list = lessonPlanService.getLessonPlanPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "陪学计划.xls", "数据", LessonPlanRespVO.class,
                        BeanUtils.toBean(list, LessonPlanRespVO.class));
    }

    @PutMapping("/audit")
    @Operation(summary = "审核提交")
    @PreAuthorize("@ss.hasPermission('als:lesson-plan:update')")
    public CommonResult<Boolean> audit(@Valid @RequestBody AuditVo auditVo) {
        lessonPlanFacade.audit(auditVo);
        return success(true);
    }

}
