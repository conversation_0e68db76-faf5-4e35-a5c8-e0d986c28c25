package com.edu.kompass.module.als.controller.admin.bind.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 绑定新增/修改 Request VO")
@Data
public class BindSaveReqVO {

    @Schema(description = "绑定表ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14249")
    private Long bindId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8253")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7145")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "绑定人", requiredMode = Schema.RequiredMode.REQUIRED, example = "29426")
    private Long bindUserId;

    @Schema(description = "解绑申请时间")
    private LocalDateTime unbindApplyTime;

    @Schema(description = "当前绑定状态：1已绑定 2已解绑", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer bindStatus;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14383")
    private Long orderId;

    @Schema(description = "绑定时间")
    private LocalDateTime bindTime;

    @Schema(description = "解绑时间")
    private LocalDateTime unbindTime;

    @Schema(description = "解绑人", requiredMode = Schema.RequiredMode.REQUIRED, example = "18073")
    private Long unbindUserId;

    @Schema(description = "解绑原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不对")
    private String unbindReason;

    @Schema(description = "解绑审核状态：0审核中 1审核通过 2驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer unbindAuditStatus;

    @Schema(description = "是否已认证家长需求",  example = "1")
    private Integer isAuthenticated;

    @Schema(description = "是否建群",  example = "1")
    private Integer isGroupChat;

    @Schema(description = "陪学类型：1课后陪读 2半天伴读 3全天伴读 4钢琴陪练", example = "1")
    private Integer lessonType;

    @Schema(description = "承诺最后服务时间", example = "2024-01-01")
    private LocalDateTime promisedLastServiceDate;

    @Schema(description = "该年级服务经验", example = "1")
    private Integer isHaveExperience;

    @Schema(description = "服务经验补充", example = "奥数")
    private String experienceExtra;

}
