package com.edu.kompass.module.als.controller.admin.lessonplan.vo;

import com.edu.kompass.framework.mybatis.core.dataobject.BaseDO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 陪学计划 VO
 *
 * <AUTHOR>
 */
@Data
public class LessonPlanVO extends BaseDO {

    /**
     * 陪学计划ID
     */
    private Long lessonPlanId;
    /**
     * 订单ID
     */
    private Long orderId;
    /**
     * 家长ID
     */
    private Long customerId;
    /**
     * 老师ID
     */
    private Long teacherId;
    /**
     * 计划课时数
     */
    private Integer planHour;
    /**
     * 已完成课时数
     */
    private Integer finishedHour;

    /**
     * 计划开始时间
     */
    private LocalDateTime startTime;

    /**
     * 计划结束时间
     */
    private LocalDateTime endTime;
    /**
     * 总体目标
     */
    private String planTarget;
    /**
     * 家长期望
     */
    private String customerExpect;
    /**
     * Q1阶段目标
     */
    private String stageQ1Target;
    /**
     * Q2阶段目标
     */
    private String stageQ2Target;
    /**
     * Q3阶段目标
     */
    private String stageQ3Target;
    /**
     * Q4阶段目标
     */
    private String stageQ4Target;
    /**
     * Q1打分
     */
    private Integer stageQ1Score;
    /**
     * Q2打分
     */
    private Integer stageQ2Score;
    /**
     * Q3打分
     */
    private Integer stageQ3Score;
    /**
     * Q4打分
     */
    private Integer stageQ4Score;
    /**
     * 审核状态
     *
     */
    private Integer planAuditStatus;
    /**
     * 审核时间
     */
    private LocalDateTime planAuditTime;
    /**
     * 审核人
     */
    private Long planAuditUserId;
    /**
     * 审核备注
     */
    private String planAuditRemark;

}
