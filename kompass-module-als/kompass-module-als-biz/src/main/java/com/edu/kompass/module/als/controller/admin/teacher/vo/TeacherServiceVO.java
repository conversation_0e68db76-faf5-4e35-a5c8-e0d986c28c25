package com.edu.kompass.module.als.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TeacherServiceVO {

    @Schema(description = "服务次数")
    private Integer serviceTimes;

    @Schema(description = "服务课时总数")
    private Integer serviceClassHour;

    @Schema(description = "服务过家长数")
    private Integer serviceCustomerNum;

    @Schema(description = "提交过日志数")
    private Integer commitLessonRecordNum;

    @Schema(description = "最近一次服务时间")
    private LocalDateTime lastServiceTime;

    @Schema(description = "最近一次抢单时间")
    private LocalDateTime lastOrderConfirmTime;

    @Schema(description = "体验成功次数")
    private Long successOrderNum;

    @Schema(description = "体验失败次数")
    private Long failOrderNum;

    @Schema(description = "提现金额")
    private BigDecimal withdrawAmount;

}
