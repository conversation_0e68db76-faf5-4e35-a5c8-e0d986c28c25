package com.edu.kompass.module.als.controller.admin.coursepackage.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 课时包新增/修改 Request VO")
@Data
public class CoursePackageSaveReqVO {

    @Schema(description = "课时包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25891")
    private Long coursePackageId;

    @Schema(description = "课时包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "课时包名称不能为空")
    private String packageName;

    @Schema(description = "课时包类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "课时包类型不能为空")
    private Integer packageType;

    @Schema(description = "总课时数", requiredMode = Schema.RequiredMode.REQUIRED, example = "60")
    @NotNull(message = "总课时数不能为空")
    private Integer lessonPeriod;

    @Schema(description = "售价", requiredMode = Schema.RequiredMode.REQUIRED, example = "11312")
    @NotNull(message = "售价不能为空")
    private BigDecimal salePrice;

    @Schema(description = "是否启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "是否启用不能为空")
    private Integer isEnable;

}
