package com.edu.kompass.module.als.controller.admin.orderregularapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 审核 Request VO")
@Data
public class RegularApplyAuditReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "ID不能为空")
    private Long id;

    @Schema(description = "反馈表审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer feedbackAuditStatus;

    @Schema(description = "反馈表审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    private String feedbackAuditRemark;

    @Schema(description = "陪学计划审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer planAuditStatus;

    @Schema(description = "陪学计划审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    private String planAuditRemark;

}
