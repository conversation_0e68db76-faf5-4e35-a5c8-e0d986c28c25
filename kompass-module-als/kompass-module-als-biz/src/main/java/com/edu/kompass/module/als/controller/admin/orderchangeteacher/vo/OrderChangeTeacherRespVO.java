package com.edu.kompass.module.als.controller.admin.orderchangeteacher.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 更换老师 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OrderChangeTeacherRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "23180")
    @ExcelProperty("主键")
    private Long orderChangeTeacherId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11212")
    @ExcelProperty("订单ID")
    private Long orderId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23761")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2067")
    @ExcelProperty("家长ID")
    private Long customerId;

    @Schema(description = "被换原因标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "被换原因标签", converter = DictConvert.class)
    @DictFormat("als_changed_reason_tags") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer reasonTags;

    @Schema(description = "具体原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不香")
    @ExcelProperty("具体原因")
    private String reason;

    @Schema(description = "对老师-好评", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("对老师-好评")
    private String goodComment;

    @Schema(description = "对老师-差评", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("对老师-差评")
    private String badComment;

    @Schema(description = "申请时间")
    @ExcelProperty("申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat("als_audit_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer auditStatus;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "10115")
    @ExcelProperty("审核人")
    private Long auditUserId;

    @Schema(description = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("审核备注")
    private String auditRemark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
