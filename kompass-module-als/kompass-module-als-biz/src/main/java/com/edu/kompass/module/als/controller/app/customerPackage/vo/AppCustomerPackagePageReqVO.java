package com.edu.kompass.module.als.controller.app.customerPackage.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 已购课时包记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppCustomerPackagePageReqVO extends PageParam {

    @Schema(description = "家长ID", example = "31048")
    private Long customerId;

    @Schema(description = "课时包名称", example = "赵六")
    private String packageName;

    @Schema(description = "总课时数")
    private BigDecimal lessonPeriod;

    @Schema(description = "总课时数")
    private Integer packageType;

    @Schema(description = "已使用课时数")
    private BigDecimal lessonPeriodUsed;

    @Schema(description = "剩余课时")
    private BigDecimal lessonPeriodRemain;

    @Schema(description = "使用状态", example = "1")
    private Integer useStatus;

    @Schema(description = "第几次购买")
    private Integer buyTimes;

}
