package com.edu.kompass.module.als.controller.admin.bind.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 绑定新增/修改 Request VO")
@Data
public class BindAuditReqVO {

    @Schema(description = "绑定ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14249")
    @NotNull(message = "绑定ID不能为空")
    private Long bindId;

    @Schema(description = "当前绑定状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "当前绑定状态不能为空")
    private Integer bindStatus;

    @Schema(description = "审核原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    @NotEmpty(message = "审核原因不能为空")
    private String reason;

    @Schema(description = "审核状态：0审核中 1审核通过 2驳回", example = "1")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    private Integer auditUserId;
}
