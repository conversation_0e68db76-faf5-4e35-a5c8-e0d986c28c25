package com.edu.kompass.module.als.controller.app.customerPackage;

import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.module.als.controller.app.customerPackage.vo.AppCustomerPackagePageReqVO;
import com.edu.kompass.module.als.controller.app.customerPackage.vo.AppCustomerPackageRespVO;
import com.edu.kompass.module.als.facade.app.customer.AppCustomerPackageFacade;
import com.edu.kompass.module.als.facade.customer.CustomerFacade;
import com.edu.kompass.module.als.service.customer.CustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static com.edu.kompass.framework.common.pojo.CommonResult.success;
import static com.edu.kompass.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "app - 已购课时包记录")
@RestController
@RequestMapping("/als/customer/package")
@Validated
@Slf4j
public class AppCustomerPackageController {

    @Autowired
    private CustomerFacade customerFacade;
    @Resource
    private CustomerService customerService;
    
    @Autowired
    private AppCustomerPackageFacade appCustomerPackageFacade;

    @GetMapping("/page")
    @Operation(summary = "购买课时包订单分页")
    public CommonResult<PageResult<AppCustomerPackageRespVO>> getCustomerPackagePage(@Valid AppCustomerPackagePageReqVO pageReqVO) {
        Long loginUserId = getLoginUserId();
        log.info("当前登录人:{}", loginUserId);
        Long customerId = customerService.getCustomerIdByMemberId(loginUserId);
        pageReqVO.setCustomerId(customerId);
        PageResult<AppCustomerPackageRespVO> customerPackagePage = appCustomerPackageFacade.getCustomerPackagePage(pageReqVO);
        return success(customerPackagePage);
    }

    @GetMapping("/get")
    @Operation(summary = "已购课时包详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<AppCustomerPackageRespVO> getCustomerPackage(@RequestParam("id") Long id) {
        AppCustomerPackageRespVO result = appCustomerPackageFacade.getCustomerPackage(id);
        return success(result);
    }
}
