package com.edu.kompass.module.als.controller.admin.customeraddapply.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课时添加申请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CustomerAddApplyPageReqVO extends PageParam {

    @Schema(description = "家长ID", example = "8215")
    private Long customerId;

    @Schema(description = "课时包名称", example = "200课时包")
    private String packageName;

    @Schema(description = "支付方式", example = "1")
    private Integer applyMethod;

    @Schema(description = "资金流转来源", example = "1")
    private Integer directionFrom;

    @Schema(description = "申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "申请人", example = "7357")
    private Long creator;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "审核人", example = "31513")
    private Long auditUserId;

}
