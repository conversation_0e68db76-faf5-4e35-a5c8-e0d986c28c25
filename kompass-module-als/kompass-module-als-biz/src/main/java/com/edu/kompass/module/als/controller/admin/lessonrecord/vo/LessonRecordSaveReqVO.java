package com.edu.kompass.module.als.controller.admin.lessonrecord.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 陪学记录新增/修改 Request VO")
@Data
public class LessonRecordSaveReqVO {

    @Schema(description = "陪学记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31357")
    private Long lessonRecordId;

    @Schema(description = "购买记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18033")
    @NotNull(message = "购买记录ID不能为空")
    private Long customerPackageId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25142")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17468")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "上课类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "上课类型不能为空")
    private Integer lessonType;

    @Schema(description = "陪学人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "陪学人数不能为空")
    private Integer childNumber;

    @Schema(description = "陪学内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "陪学内容不能为空")
    private String lessonContent;

    @Schema(description = "陪学记录状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "陪学记录状态不能为空")
    private Integer recordStatus;

    @Schema(description = "填写进度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "填写进度不能为空")
    private Integer process;

    @Schema(description = "课前准备项打分", requiredMode = Schema.RequiredMode.REQUIRED, example = "[4,5,5]")
    @NotEmpty(message = "课前准备项打分不能为空")
    private List<Integer> prepareScore;

    @Schema(description = "课后项打分", requiredMode = Schema.RequiredMode.REQUIRED, example = "[4,5]")
    @NotEmpty(message = "课后项打分不能为空")
    private List<Integer> summaryScore;

    @Schema(description = "课前准备事项", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "课前准备事项不能为空")
    private List<Integer> prepareItem;

    @Schema(description = "课后总结事项", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "课后总结事项不能为空")
    private List<Integer> summaryItem;

    @Schema(description = "薄弱点记录", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "薄弱点记录不能为空")
    private String weakSpot;

    @Schema(description = "行为表现评价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "行为表现评价不能为空")
    private String showEvaluate;

    @Schema(description = "陪学反思", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "陪学反思不能为空")
    private String reflect;

    @Schema(description = "给家长留言", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "给家长留言不能为空")
    private String leaveWord;

    @Schema(description = "开始打卡时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始打卡时间不能为空")
    private LocalDateTime startTime;

    @Schema(description = "结束打卡时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束打卡时间不能为空")
    private LocalDateTime endTime;

    @Schema(description = "陪学时长", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "陪学时长不能为空")
    private BigDecimal scheduleHour;

    @Schema(description = "下次上课时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "下次上课时间不能为空")
    private LocalDateTime nextTime;

    @Schema(description = "提交时间")
    private LocalDateTime commitTime;

}
