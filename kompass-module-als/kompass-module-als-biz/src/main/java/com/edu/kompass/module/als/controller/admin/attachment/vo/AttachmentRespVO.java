package com.edu.kompass.module.als.controller.admin.attachment.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 附件 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AttachmentRespVO {

    @Schema(description = "附件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4036")
    @ExcelProperty("附件ID")
    private Long attachmentId;

    @Schema(description = "附件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("附件类型")
    private Integer attachmentType;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("业务类型")
    private Integer bizType;

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9916")
    @ExcelProperty("业务ID")
    private Long bizId;

    @Schema(description = "附件url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("附件url")
    private String url;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
