package com.edu.kompass.module.als.service.teacherfav;

import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.module.als.controller.admin.teacherfav.vo.TeacherFavPageReqVO;
import com.edu.kompass.module.als.controller.admin.teacherfav.vo.TeacherFavSaveReqVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppOrderPageReqVO;
import com.edu.kompass.module.als.dal.dataobject.order.OrderExtDo;
import com.edu.kompass.module.als.dal.dataobject.teacherfav.TeacherFavDO;
import com.edu.kompass.module.als.enums.FavTypeEnum;

import javax.validation.Valid;

/**
 * 老师收藏 Service 接口
 *
 * <AUTHOR>
 */
public interface TeacherFavService {

    /**
     * 创建老师收藏
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTeacherFav(@Valid TeacherFavSaveReqVO createReqVO);

    /**
     * 更新老师收藏
     *
     * @param updateReqVO 更新信息
     */
    void updateTeacherFav(@Valid TeacherFavSaveReqVO updateReqVO);

    /**
     * 删除老师收藏
     *
     * @param id 编号
     */
    void deleteTeacherFav(Long id);

    /**
     * 获得老师收藏
     *
     * @param id 编号
     * @return 老师收藏
     */
    TeacherFavDO getTeacherFav(Long id);

    /**
     * 获得老师收藏分页
     *
     * @param pageReqVO 分页查询
     * @return 老师收藏分页
     */
    PageResult<TeacherFavDO> getTeacherFavPage(TeacherFavPageReqVO pageReqVO);

    /**
     * 判断是否收藏该订单
     * @param orderId
     * @param teacherId
     * @return
     */
    TeacherFavDO getTeacherIsFav(Long orderId, Long teacherId, FavTypeEnum favTypeEnum);

    /**
     * 自定义收藏订单查询
     * @param pageReqVO
     * @return
     */
    PageResult<OrderExtDo> getOrderCenterFavPage(AppOrderPageReqVO pageReqVO);
}
