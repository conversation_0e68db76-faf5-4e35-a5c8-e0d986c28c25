package com.edu.kompass.module.als.controller.admin.orderregularapply.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 正式课申请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderRegularApplyPageReqVO extends PageParam {

    @Schema(description = "订单ID", example = "22856")
    private Long orderId;

    @Schema(description = "家长ID", example = "11830")
    private Long customerId;

    @Schema(description = "老师ID", example = "12397")
    private Long teacherId;

    @Schema(description = "体验课反馈简述")
    private String feedbackDesc;

    @Schema(description = "陪学计划ID", example = "25424")
    private Long planLessonId;

    @Schema(description = "反馈表审核状态", example = "1")
    private Integer feedbackAuditStatus;

    @Schema(description = "反馈表审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] feedbackAuditTime;

    @Schema(description = "反馈表审核人", example = "2796")
    private Long feedbackAuditUserId;

    @Schema(description = "反馈表审核备注", example = "你说的对")
    private String feedbackAuditRemark;

    @Schema(description = "来自家长的评价")
    private Integer customerEvaluation;

    @Schema(description = "正式课陪学计划审核状态", example = "2")
    private Integer planAuditStatus;

    @Schema(description = "正式课陪学计划审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] planAuditTime;

    @Schema(description = "正式课陪学计划审核人", example = "3878")
    private Long planAuditUserId;

    @Schema(description = "正式课陪学计划审核备注", example = "你猜")
    private String planAuditRemark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
