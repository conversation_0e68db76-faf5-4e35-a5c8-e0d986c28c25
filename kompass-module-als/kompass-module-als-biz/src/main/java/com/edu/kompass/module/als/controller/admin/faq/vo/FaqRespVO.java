package com.edu.kompass.module.als.controller.admin.faq.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 常见问题解答 Response VO")
@Data
@ExcelIgnoreUnannotated
public class FaqRespVO {

    @Schema(description = "常见问题ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32392")
    @ExcelProperty("常见问题ID")
    private Long faqId;

    @Schema(description = "可见方", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "可见方", converter = DictConvert.class)
    @DictFormat("als_faq_who") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer faqWho;

    @Schema(description = "问题分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "问题分类", converter = DictConvert.class)
    @DictFormat("als_faq_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer faqType;

    @Schema(description = "问题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问题")
    private String faqQuestion;

    @Schema(description = "解答", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("解答")
    private String faqAnswer;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("common_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer faqStatus;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
