package com.edu.kompass.module.als.controller.admin.orderregularapply.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 正式课申请 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OrderRegularApplyRespVO {

    @Schema(description = "正式课申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5357")
    @ExcelProperty("正式课申请ID")
    private Long orderRegularApplyId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "22856")
    @ExcelProperty("订单ID")
    private Long orderId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11830")
    @ExcelProperty("家长ID")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12397")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "体验课反馈简述", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("体验课反馈简述")
    private String feedbackDesc;

    @Schema(description = "体验课反馈表", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("体验课反馈表")
    private String feedbackUrl;

    @Schema(description = "正式课陪学计划表", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("正式课陪学计划表")
    private String planUrl;

    @Schema(description = "陪学计划ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25424")
    @ExcelProperty("陪学计划ID")
    private Long planLessonId;

    @Schema(description = "陪学公约", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("陪学公约")
    private String agreedUrl;

    @Schema(description = "反馈表审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "反馈表审核状态", converter = DictConvert.class)
    @DictFormat("als_audit_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer feedbackAuditStatus;

    @Schema(description = "反馈表审核时间")
    @ExcelProperty("反馈表审核时间")
    private LocalDateTime feedbackAuditTime;

    @Schema(description = "反馈表审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "2796")
    @ExcelProperty("反馈表审核人")
    private Long feedbackAuditUserId;

    @Schema(description = "反馈表审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("反馈表审核备注")
    private String feedbackAuditRemark;

    @Schema(description = "来自家长的评价", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "来自家长的评价", converter = DictConvert.class)
    @DictFormat("als_customer_evaluation") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer customerEvaluation;

    @Schema(description = "正式课陪学计划审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "正式课陪学计划审核状态", converter = DictConvert.class)
    @DictFormat("als_audit_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer planAuditStatus;

    @Schema(description = "正式课陪学计划审核时间")
    @ExcelProperty("正式课陪学计划审核时间")
    private LocalDateTime planAuditTime;

    @Schema(description = "正式课陪学计划审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "3878")
    @ExcelProperty("正式课陪学计划审核人")
    private Long planAuditUserId;

    @Schema(description = "正式课陪学计划审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("正式课陪学计划审核备注")
    private String planAuditRemark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
