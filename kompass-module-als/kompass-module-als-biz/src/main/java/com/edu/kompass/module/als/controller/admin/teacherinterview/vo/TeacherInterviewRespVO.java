package com.edu.kompass.module.als.controller.admin.teacherinterview.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 老师面试 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TeacherInterviewRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12668")
    @ExcelProperty("主键")
    private Long teacherInterviewId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2496")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("老师姓名")
    private String teacherName;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "17300001111")
    @ExcelProperty("手机号")
    private String teacherPhone;

    @Schema(description = "老师等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "老师等级", converter = DictConvert.class)
    @DictFormat("als_teacher_level") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer level;

    @Schema(description = "面试官ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long interviewer;

    @Schema(description = "面试官", requiredMode = Schema.RequiredMode.REQUIRED)
    private String interviewerName;

    @Schema(description = "面试官评价", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("面试官评价")
    private String interviewerEvaluate;

    @Schema(description = "面试时间")
    @ExcelProperty("面试时间")
    private LocalDateTime interviewTime;

    @Schema(description = "师资备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("师资备注")
    private String teacherRemark;

    @Schema(description = "基本素质", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "基本素质", converter = DictConvert.class)
    @DictFormat("als_quality_basic") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> qualityBasic;

    @Schema(description = "综合素质评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("综合素质评分")
    private List<Integer> qualityComprehensive;

    @Schema(description = "试讲评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "试讲评分", converter = DictConvert.class)
    @DictFormat("als_quality_lecture") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> qualityLecture;

    @Schema(description = "综合评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("综合评分")
    private BigDecimal finallyScore;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
