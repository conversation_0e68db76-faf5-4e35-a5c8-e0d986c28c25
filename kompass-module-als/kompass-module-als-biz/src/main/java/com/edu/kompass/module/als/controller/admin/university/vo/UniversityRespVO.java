package com.edu.kompass.module.als.controller.admin.university.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 大学信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UniversityRespVO {

    @Schema(description = "大学ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28799")
    @ExcelProperty("大学ID")
    private Long universityId;

    @Schema(description = "大学名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("大学名称")
    private String universityName;

    @Schema(description = "标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标签")
    private String universityTag;

    @Schema(description = "校徽", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("校徽")
    private String logo;

    @Schema(description = "校徽新地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("校徽新地址")
    private String logoNew;

    @Schema(description = "所在省份", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("所在省份")
    private String province;

    @Schema(description = "城市", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("城市")
    private String location;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
