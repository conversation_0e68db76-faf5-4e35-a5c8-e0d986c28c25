package com.edu.kompass.module.als.controller.admin.teacheraccountchange.vo;

import com.edu.kompass.framework.mybatis.core.dataobject.BaseDO;
import com.edu.kompass.framework.mybatis.core.dataobject.BaseVO;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 老师账户变更记录 VO
 *
 * <AUTHOR>
 */
@Data
public class TeacherAccountChangeVO extends BaseVO {

    /**
     * 老师ID
     */
    private Long teacherId;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 变更备注
     */
    private String remark;
    /**
     * 变更业务类型
     *
     */
    private Integer businessType;
    /**
     * 业务ID
     */
    private Long businessId;

}
