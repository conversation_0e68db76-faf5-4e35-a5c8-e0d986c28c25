package com.edu.kompass.module.als.controller.admin.teachercertificate.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 老师证书 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TeacherCertificateRespVO {

    @Schema(description = "证书ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16607")
    @ExcelProperty("证书ID")
    private Long certificateId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2144")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("老师姓名")
    private String teacherName;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "性别", converter = DictConvert.class)
    @DictFormat("als_sex") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String teacherSex;

    @Schema(description = "证书照片URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("证书照片URL")
    private String picUrl;

    @Schema(description = "身份证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String teacherIdNumber;

    @Schema(description = "证书编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("证书编号")
    private String certificateNo;

    @Schema(description = "证书状态 0有效 1无效-已过期 2无效-已注销", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "证书状态 0有效 1无效-已过期 2无效-已注销", converter = DictConvert.class)
    @DictFormat("als_certificate_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer certificateStatus;

    @Schema(description = "有效期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("有效期")
    private String validTime;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
