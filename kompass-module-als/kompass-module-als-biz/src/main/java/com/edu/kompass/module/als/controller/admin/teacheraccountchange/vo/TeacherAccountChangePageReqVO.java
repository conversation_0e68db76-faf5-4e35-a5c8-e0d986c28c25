package com.edu.kompass.module.als.controller.admin.teacheraccountchange.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 老师账户变更记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherAccountChangePageReqVO extends PageParam {

    @Schema(description = "变更ID", example = "18214")
    private Long teacherAccountChangeId;

    @Schema(description = "老师ID", example = "13993")
    private Long teacherId;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "变更后余额")
    private BigDecimal balance;

    @Schema(description = "变更备注", example = "随便")
    private String remark;

    @Schema(description = "变更业务类型", example = "2")
    private Integer businessType;

    @Schema(description = "业务ID", example = "6262")
    private Long businessId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
