package com.edu.kompass.module.als.controller.admin.refundapply;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.audit.AuditVo;
import com.edu.kompass.module.als.controller.admin.refundapply.vo.RefundApplyPageReqVO;
import com.edu.kompass.module.als.controller.admin.refundapply.vo.RefundApplyRespVO;
import com.edu.kompass.module.als.controller.admin.refundapply.vo.RefundApplySaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.refundapply.RefundApplyDO;
import com.edu.kompass.module.als.facade.customer.RefundFacade;
import com.edu.kompass.module.als.service.refundapply.RefundApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 家长退款申请")
@RestController
@RequestMapping("/als/refund-apply")
@Validated
public class RefundApplyController {

    @Resource
    private RefundApplyService refundApplyService;

    @Resource
    private RefundFacade refundFacade;

    @PostMapping("/create")
    @Operation(summary = "创建家长退款申请")
    @PreAuthorize("@ss.hasPermission('als:refund-apply:create')")
    public CommonResult<Long> createRefundApply(@Valid @RequestBody RefundApplySaveReqVO createReqVO) {
        return success(refundApplyService.createRefundApply(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新家长退款申请")
    @PreAuthorize("@ss.hasPermission('als:refund-apply:update')")
    public CommonResult<Boolean> updateRefundApply(@Valid @RequestBody RefundApplySaveReqVO updateReqVO) {
        refundApplyService.updateRefundApply(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除家长退款申请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:refund-apply:delete')")
    public CommonResult<Boolean> deleteRefundApply(@RequestParam("id") Long id) {
        refundApplyService.deleteRefundApply(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得家长退款申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:refund-apply:query')")
    public CommonResult<RefundApplyRespVO> getRefundApply(@RequestParam("id") Long id) {
        RefundApplyDO refundApply = refundApplyService.getRefundApply(id);
        return success(BeanUtils.toBean(refundApply, RefundApplyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得家长退款申请分页")
    @PreAuthorize("@ss.hasPermission('als:refund-apply:query')")
    public CommonResult<PageResult<RefundApplyRespVO>> getRefundApplyPage(@Valid RefundApplyPageReqVO pageReqVO) {
        PageResult<RefundApplyRespVO> pageResult = refundFacade.getRefundApplyPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出家长退款申请 Excel")
    @PreAuthorize("@ss.hasPermission('als:refund-apply:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportRefundApplyExcel(@Valid RefundApplyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RefundApplyDO> list = refundApplyService.getRefundApplyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "家长退款申请.xls", "数据", RefundApplyRespVO.class,
                        BeanUtils.toBean(list, RefundApplyRespVO.class));
    }

    @PutMapping("/audit")
    @Operation(summary = "退款审核")
    @PreAuthorize("@ss.hasPermission('als:refund-apply:update')")
    public CommonResult<Boolean> audit(@Valid @RequestBody AuditVo auditVo) {
        refundFacade.auditRefund(auditVo);
        return success(true);
    }


}
