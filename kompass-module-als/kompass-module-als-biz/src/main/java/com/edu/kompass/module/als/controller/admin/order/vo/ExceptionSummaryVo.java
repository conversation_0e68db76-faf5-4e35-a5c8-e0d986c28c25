package com.edu.kompass.module.als.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.stereotype.Service;

@Service
@Data
public class ExceptionSummaryVo {

    @Schema(description = "家长姓名")
    private String customerName;

    @Schema(description = "请假原因")
    private String leaveReason;

    @Schema(description = "换老师原因")
    private String changeTeacherReason;

    @Schema(description = "正在服务备注")
    private String serviceRemark;

    @Schema(description = "课时用尽原因")
    private String useEndReason;
}
