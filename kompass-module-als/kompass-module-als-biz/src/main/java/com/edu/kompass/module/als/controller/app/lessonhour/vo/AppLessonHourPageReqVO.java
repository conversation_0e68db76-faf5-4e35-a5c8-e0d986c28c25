package com.edu.kompass.module.als.controller.app.lessonhour.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "app - 课时记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AppLessonHourPageReqVO extends PageParam {

    @Schema(description = "陪学记录ID", example = "32391")
    private Long lessonRecordId;
    
    private String keyword;

    @Schema(description = "购买套餐ID", example = "28056")
    private Long customerPackageId;
    
    private List<Long> customerPackageIds;

    @Schema(description = "上课类型", example = "2")
    private Integer lessonType;

    @Schema(description = "人数", example = "1")
    private Integer childNumber;

    @Schema(description = "家长ID", example = "17477")
    private Long customerId;
    
    private Long customerMemberId;

    @Schema(description = "老师ID", example = "27313")
    private Long teacherId;
    private List<Long> teacherIds;

    @Schema(description = "上课时长(h) 押金时拆分", example = "1")
    private BigDecimal timeHour;

    @Schema(description = "倍数", example = "1")
    private BigDecimal multiple;

    @Schema(description = "计算课时", example = "1")
    private BigDecimal lessonHour;

    @Schema(description = "总课时", example = "1")
    private BigDecimal classHour;

    @Schema(description = "老师-课时单价", example = "31904")
    private BigDecimal teacherPrice;

    @Schema(description = "老师-小时单价(课", example = "13605")
    private BigDecimal teacherHourPrice;

    @Schema(description = "老师-附加总费用", example = "11")
    private BigDecimal teacherExtraCharge;

    @Schema(description = "老师-总薪资", example = "22")
    private BigDecimal teacherAmount;

    @Schema(description = "家长-课时单价", example = "26922")
    private BigDecimal customerPrice;

    @Schema(description = "家长-扣费金额", example = "22")
    private BigDecimal customerCost;

    @Schema(description = "下次上课时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] nextTime;

    @Schema(description = "课时记录状态", example = "2")
    private Integer recordStatus;

    private List<Integer> recordStatusList;

    /**
     * 确认状态
     * 0-未确认 1-已确认
     */
    private Integer confirmStatus;
    
    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "审核人", example = "21873")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "随便")
    private String auditRemark;

    @Schema(description = "押金处理方式", example = "1")
    private Integer dealMethod;

    @Schema(description = "处理人", example = "10901")
    private Long dealUserId;

    @Schema(description = "处理时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] dealTime;

    @Schema(description = "备注时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] remarkTime;

    @Schema(description = "备注人", example = "27852")
    private Long remarkUserId;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
