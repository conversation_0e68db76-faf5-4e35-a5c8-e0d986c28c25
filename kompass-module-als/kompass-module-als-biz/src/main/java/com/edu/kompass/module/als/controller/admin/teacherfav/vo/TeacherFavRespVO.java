package com.edu.kompass.module.als.controller.admin.teacherfav.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 老师收藏 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TeacherFavRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "4276")
    @ExcelProperty("主键")
    private Long teacherFavId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "66")
    @ExcelProperty("订单ID")
    private Long orderId;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型")
    private Integer type;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23449")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否删除", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否删除")
    private Integer deleted;

}