package com.edu.kompass.module.als.controller.admin.teacheraccountchange;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.teacheraccountchange.vo.TeacherAccountChangePageReqVO;
import com.edu.kompass.module.als.controller.admin.teacheraccountchange.vo.TeacherAccountChangeRespVO;
import com.edu.kompass.module.als.controller.admin.teacheraccountchange.vo.TeacherAccountChangeSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.teacheraccountchange.TeacherAccountChangeDO;
import com.edu.kompass.module.als.service.teacheraccountchange.TeacherAccountChangeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 老师账户变更记录")
@RestController
@RequestMapping("/als/teacher-account-change")
@Validated
public class TeacherAccountChangeController {

    @Resource
    private TeacherAccountChangeService teacherAccountChangeService;

    @PostMapping("/create")
    @Operation(summary = "创建老师账户变更记录")
    @PreAuthorize("@ss.hasPermission('als:teacher-account-change:create')")
    public CommonResult<Long> createTeacherAccountChange(@Valid @RequestBody TeacherAccountChangeSaveReqVO createReqVO) {
        return success(teacherAccountChangeService.createTeacherAccountChange(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新老师账户变更记录")
    @PreAuthorize("@ss.hasPermission('als:teacher-account-change:update')")
    public CommonResult<Boolean> updateTeacherAccountChange(@Valid @RequestBody TeacherAccountChangeSaveReqVO updateReqVO) {
        teacherAccountChangeService.updateTeacherAccountChange(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除老师账户变更记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:teacher-account-change:delete')")
    public CommonResult<Boolean> deleteTeacherAccountChange(@RequestParam("id") Long id) {
        teacherAccountChangeService.deleteTeacherAccountChange(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得老师账户变更记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher-account-change:query')")
    public CommonResult<TeacherAccountChangeRespVO> getTeacherAccountChange(@RequestParam("id") Long id) {
        TeacherAccountChangeDO teacherAccountChange = teacherAccountChangeService.getTeacherAccountChange(id);
        return success(BeanUtils.toBean(teacherAccountChange, TeacherAccountChangeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得老师账户变更记录分页")
    @PreAuthorize("@ss.hasPermission('als:teacher-account-change:query')")
    public CommonResult<PageResult<TeacherAccountChangeRespVO>> getTeacherAccountChangePage(@Valid TeacherAccountChangePageReqVO pageReqVO) {
        PageResult<TeacherAccountChangeDO> pageResult = teacherAccountChangeService.getTeacherAccountChangePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TeacherAccountChangeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出老师账户变更记录 Excel")
    @PreAuthorize("@ss.hasPermission('als:teacher-account-change:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTeacherAccountChangeExcel(@Valid TeacherAccountChangePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TeacherAccountChangeDO> list = teacherAccountChangeService.getTeacherAccountChangePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "老师账户变更记录.xls", "数据", TeacherAccountChangeRespVO.class,
                        BeanUtils.toBean(list, TeacherAccountChangeRespVO.class));
    }

}
