package com.edu.kompass.module.als.controller.admin.attachment.vo;

import com.edu.kompass.framework.mybatis.core.dataobject.BaseDO;
import com.edu.kompass.module.als.enums.AttachmentTypeEnum;
import lombok.Data;

/**
 * 附件 VO
 *
 * <AUTHOR>
 */
@Data
public class AttachmentVO extends BaseDO {

    /**
     * 附件ID
     */
    private Long attachmentId;
    /**
     * 附件类型
     * @see AttachmentTypeEnum
     */
    private Integer attachmentType;
    /**
     * 业务类型
     */
    private Integer bizType;
    /**
     * 业务ID
     */
    private Long bizId;
    /**
     * 附件url
     */
    private String url;

}
