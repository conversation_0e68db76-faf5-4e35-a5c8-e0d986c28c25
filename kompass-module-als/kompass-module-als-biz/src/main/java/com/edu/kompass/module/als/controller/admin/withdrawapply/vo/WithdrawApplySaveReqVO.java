package com.edu.kompass.module.als.controller.admin.withdrawapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 提现申请新增/修改 Request VO")
@Data
public class WithdrawApplySaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31414")
    private Long withdrawApplyId;

    @Schema(description = "会员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "783")
    @NotNull(message = "会员ID不能为空")
    private Long memberId;

    @Schema(description = "提现金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "提现金额不能为空")
    private BigDecimal amount;

    @Schema(description = "提现手续费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "提现手续费不能为空")
    private BigDecimal fee;

    @Schema(description = "提现类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "提现类型不能为空")
    private Integer type;

    @Schema(description = "账号")
    private String accountNo;

    @Schema(description = "收款码", example = "https://www.iocoder.cn")
    private String accountQrCodeUrl;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "4192")
    @NotNull(message = "审核人不能为空")
    private Long auditUserId;

    @Schema(description = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "审核备注不能为空")
    private String auditRemark;

    @Schema(description = "提现状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "提现状态不能为空")
    private Integer withdrawStatus;

}
