package com.edu.kompass.module.als.controller.admin.teacherability.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Schema(description = "管理后台 - 老师能力新增/修改 Request VO")
@Data
public class TeacherAbilitySaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19666")
    private Long teacherAbilityId;

    @Schema(description = "外语", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "外语不能为空")
    private List<String> foreignLanguage;

    @Schema(description = "外语证明", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "外语证明不能为空")
    private String foreignCertificate;

    @Schema(description = "四级分数")
    private Integer gradeFour;

    @Schema(description = "六级分数")
    private Integer gradeSix;

    @Schema(description = "高考英语")
    private Integer englishScore;

    @Schema(description = "雅思")
    private BigDecimal ieltsScore;

    @Schema(description = "托福")
    private BigDecimal toeflScore;

    @Schema(description = "钢琴等级")
    private String pianoLevel;

    @Schema(description = "钢琴证书颁证方")
    private String pianoCertificateIssuer;

    @Schema(description = "其他技能证书及获奖情况")
    private String otherCertificate;

    @Schema(description = "在校获奖情况", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Integer> schoolAwards;

    @Schema(description = "在校获奖等级", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Integer> schoolAwardsLevel;

    @Schema(description = "补充奖项", requiredMode = Schema.RequiredMode.REQUIRED)
    private String schoolAwardsExtra;

    @Schema(description = "特长", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Integer> forte;

    @Schema(description = "其他特长", requiredMode = Schema.RequiredMode.REQUIRED)
    private String forteExtra;

    @Schema(description = "家教经历", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "家教经历不能为空")
    private String experience;

    @Schema(description = "教学方法", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "教学方法不能为空")
    private String teachingMethod;

    @Schema(description = "作业辅导科目擅长排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "作业辅导科目擅长排序不能为空")
    private List<String> teachScopeRank;

}
