package com.edu.kompass.module.als.controller.admin.university;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

import com.edu.kompass.framework.excel.core.util.ExcelUtils;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.*;

import com.edu.kompass.module.als.controller.admin.university.vo.*;
import com.edu.kompass.module.als.dal.dataobject.university.UniversityDO;
import com.edu.kompass.module.als.service.university.UniversityService;

@Tag(name = "管理后台 - 大学信息")
@RestController
@RequestMapping("/als/university")
@Validated
public class UniversityController {

    @Resource
    private UniversityService universityService;

    @PostMapping("/create")
    @Operation(summary = "创建大学信息")
    @PermitAll
    public CommonResult<Long> createUniversity( @RequestBody UniversitySaveReqVO createReqVO) {
        return success(universityService.createUniversity(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新大学信息")
    @PreAuthorize("@ss.hasPermission('als:university:update')")
    public CommonResult<Boolean> updateUniversity(@Valid @RequestBody UniversitySaveReqVO updateReqVO) {
        universityService.updateUniversity(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除大学信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:university:delete')")
    public CommonResult<Boolean> deleteUniversity(@RequestParam("id") Long id) {
        universityService.deleteUniversity(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得大学信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:university:query')")
    public CommonResult<UniversityRespVO> getUniversity(@RequestParam("id") Long id) {
        UniversityDO university = universityService.getUniversity(id);
        return success(BeanUtils.toBean(university, UniversityRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得大学信息分页")
    @PreAuthorize("@ss.hasPermission('als:university:query')")
    public CommonResult<PageResult<UniversityRespVO>> getUniversityPage(@Valid UniversityPageReqVO pageReqVO) {
        PageResult<UniversityDO> pageResult = universityService.getUniversityPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UniversityRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出大学信息 Excel")
    @PreAuthorize("@ss.hasPermission('als:university:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportUniversityExcel(@Valid UniversityPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<UniversityDO> list = universityService.getUniversityPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "大学信息.xls", "数据", UniversityRespVO.class,
                        BeanUtils.toBean(list, UniversityRespVO.class));
    }

}
