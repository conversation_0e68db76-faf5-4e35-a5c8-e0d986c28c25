package com.edu.kompass.module.als.controller.admin.teacherfav.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 老师收藏新增/修改 Request VO")
@Data
public class TeacherFavSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "4276")
    private Long teacherFavId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "66")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "类型不能为空")
    private Integer type;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23449")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

}