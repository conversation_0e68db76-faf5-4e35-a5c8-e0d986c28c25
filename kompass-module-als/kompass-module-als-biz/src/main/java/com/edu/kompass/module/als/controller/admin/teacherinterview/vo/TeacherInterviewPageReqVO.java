package com.edu.kompass.module.als.controller.admin.teacherinterview.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 老师面试分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherInterviewPageReqVO extends PageParam {

    @Schema(description = "老师ID", example = "2496")
    private Long teacherId;

    @Schema(description = "老师姓名", example = "张三")
    private String teacherName;

    @Schema(description = "手机号", example = "17300009999")
    private String teacherPhone;

    @Schema(description = "老师等级")
    private Integer level;

    @Schema(description = "面试官")
    private Integer interviewer;

    @Schema(description = "面试官名称")
    private String interviewerName;

    @Schema(description = "面试官评价")
    private String interviewerEvaluate;

    @Schema(description = "面试时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] interviewTime;

    @Schema(description = "师资备注", example = "你说的对")
    private String teacherRemark;

    @Schema(description = "基本素质")
    private String qualityBasic;

    @Schema(description = "综合素质评分")
    private String qualityComprehensive;

    @Schema(description = "试讲评分")
    private String qualityLecture;

    @Schema(description = "综合评分")
    private BigDecimal finallyScore;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
