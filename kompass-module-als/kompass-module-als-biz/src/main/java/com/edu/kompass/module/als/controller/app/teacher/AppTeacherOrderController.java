package com.edu.kompass.module.als.controller.app.teacher;

import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.idempotent.core.annotation.Idempotent;
import com.edu.kompass.module.als.controller.app.customer.vo.AppOrderPageReqVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppOrderRespVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppTeacherFavRespVO;
import com.edu.kompass.module.als.controller.app.teacher.vo.AppTeacherReqVO;
import com.edu.kompass.module.als.enums.FavTypeEnum;
import com.edu.kompass.module.als.facade.app.order.AppOrderFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.edu.kompass.framework.common.pojo.CommonResult.success;
import static com.edu.kompass.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "老师app - 我的订单")
@RestController
@RequestMapping("/als/teacher/order")
@Validated
public class AppTeacherOrderController {

    @Resource
    private AppOrderFacade appOrderFacade;

    @GetMapping("/page")
    @Operation(summary = "体验单分页列表")
    public CommonResult<PageResult<AppOrderRespVO>> getOrderPage(@Valid AppOrderPageReqVO pageReqVO) {
        pageReqVO.setTeacherMemberId(getLoginUserId());
        PageResult<AppOrderRespVO> orderPage = appOrderFacade.getTeacherOrderPage(pageReqVO);
        return success(orderPage);
    }

    @GetMapping("/myFav")
    @Operation(summary = "我的收藏分页")
    public CommonResult<PageResult<AppOrderRespVO>> getMyFav(@Valid AppOrderPageReqVO pageReqVO) {
        pageReqVO.setTeacherMemberId(getLoginUserId());
        PageResult<AppOrderRespVO> orderPage = appOrderFacade.getMyFav(pageReqVO);
        return success(orderPage);
    }

    @GetMapping("/fav")
    @Operation(summary = "收藏订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppTeacherFavRespVO> fav(@RequestParam("id") Long id) {
        Long teacherMemberId = getLoginUserId();
        AppTeacherFavRespVO favRespVO = appOrderFacade.fav(id,teacherMemberId, FavTypeEnum.COLLECTION);
        return success(favRespVO);
    }

    @GetMapping("/cancelFav")
    @Operation(summary = "取消收藏订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<Boolean> cancelFav(@RequestParam("id") Long id) {
        Long teacherMemberId = getLoginUserId();
        appOrderFacade.cancelFav(id,teacherMemberId);
        return success(true);
    }

    @GetMapping("/centerPage")
    @Operation(summary = "接单大厅")
    public CommonResult<PageResult<AppOrderRespVO>> getCenterPage(@Valid AppOrderPageReqVO pageReqVO) {
        pageReqVO.setTeacherMemberId(getLoginUserId());
        PageResult<AppOrderRespVO> orderPage = appOrderFacade.getOrderCenterPage(pageReqVO);
        return success(orderPage);
    }

    @GetMapping("/centerDetail")
    @Operation(summary = "接单大厅-详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppOrderRespVO> getOrderCenterDetail(@RequestParam("id") Long id) {
        Long teacherMemberId = getLoginUserId();
        AppOrderRespVO order = appOrderFacade.getOrderCenterDetail(id,teacherMemberId);

        return success(order);
    }

    @GetMapping("/applyOrder")
    @Operation(summary = "申请接单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Idempotent(timeout = 10, message = "提交申请中，请勿重复提交")
    public CommonResult<Long> applyOrder(@RequestParam("id") Long id) {
        Long teacherMemberId = getLoginUserId();
        Long orderConfirmId = appOrderFacade.applyOrder(id, teacherMemberId);
        return success(orderConfirmId);
    }

    @GetMapping("/get")
    @Operation(summary = "体验单详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppOrderRespVO> getOrder(@RequestParam("id") Long id) {
        Long loginUserId = getLoginUserId();
        AppTeacherReqVO reqVO = new AppTeacherReqVO(id);
        reqVO.setTeacherMemberId(loginUserId);
        AppOrderRespVO order = appOrderFacade.getOrder(reqVO);
        return success(order);
    }
}
