package com.edu.kompass.module.als.controller.admin.customerpackage.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 已购课时包记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CustomerPackageRespVO {

    @Schema(description = "购买记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5792")
    private Long customerPackageId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31048")
    private Long customerId;

    @Schema(description = "家长姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String customerName;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13012345678")
    private String customerPhone;

    @Schema(description = "课时包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String packageName;

    @Schema(description = "课时包类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer packageType;

    @Schema(description = "实付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal actualAmount;

    @Schema(description = "总课时数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lessonPeriod;

    @Schema(description = "已使用课时数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lessonPeriodUsed;

    @Schema(description = "剩余课时", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lessonPeriodRemain;

    @Schema(description = "使用状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("als_use_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer useStatus;

    @Schema(description = "第几次购买", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer buyTimes;

    @Schema(description = "购买时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private String createTime;

    @Schema(description = "运营负责人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String headOperateUser;

    @Schema(description = "市场负责人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String headMarketUser;

}
