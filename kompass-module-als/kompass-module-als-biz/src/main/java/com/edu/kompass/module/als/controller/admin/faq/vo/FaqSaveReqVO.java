package com.edu.kompass.module.als.controller.admin.faq.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 常见问题解答新增/修改 Request VO")
@Data
public class FaqSaveReqVO {

    @Schema(description = "常见问题ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32392")
    private Long faqId;

    @Schema(description = "可见方", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "可见方不能为空")
    private Integer faqWho;

    @Schema(description = "问题分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "问题分类不能为空")
    private Integer faqType;

    @Schema(description = "问题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "问题不能为空")
    private String faqQuestion;

    @Schema(description = "解答", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "解答不能为空")
    private String faqAnswer;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态不能为空")
    private Integer faqStatus;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    private String remark;

}
