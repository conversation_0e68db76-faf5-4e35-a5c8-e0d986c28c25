package com.edu.kompass.module.als.controller.admin.orderconfirm.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 接单确认 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OrderConfirmRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31244")
    @ExcelProperty("主键")
    private Long orderConfirmId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4414")
    @ExcelProperty("订单ID")
    private Long orderId;

    @Schema(description = "是否暂停接单", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer isSuspend;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25149")
    @ExcelProperty("家长ID")
    private Long customerId;

    @Schema(description = "家长姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "25149")
    @ExcelProperty("家长姓名")
    private String customerName;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11844")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "11844")
    @ExcelProperty("老师姓名")
    private String teacherName;

    @Schema(description = "处理状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "处理状态", converter = DictConvert.class)
    @DictFormat("als_deal_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer dealStatus;

    @Schema(description = "拒绝原因ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "拒绝原因ID", converter = DictConvert.class)
    @DictFormat("als_reject_reason") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer rejectReasonId;

    @Schema(description = "自定义不合适原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
    @ExcelProperty("自定义不合适原因")
    private String customReason;

    @Schema(description = "处理时间")
    @ExcelProperty("处理时间")
    private LocalDateTime dealTime;

    @Schema(description = "处理人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long dealUserId;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED, example = "张安")
    @ExcelProperty("处理人")
    private String dealUserName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
