package com.edu.kompass.module.als.controller.app.lessonhour.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "app - 课时记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppLessonHourRespVO {

    @Schema(description = "课时记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "30176")
    private Long lessonHourId;

    @Schema(description = "陪学记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32391")
    private Long lessonRecordId;

    @Schema(description = "购买套餐ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28056")
    private Long customerPackageId;
    private String packageName;

    @Schema(description = "上课类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @DictFormat("als_lesson_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer lessonType;
    
    @Schema(description = "上课类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String lessonTypeStr;

    @Schema(description = "人数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat("als_child_number") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer childNumber;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17477")
    private Long customerId;

    @Schema(description = "家长姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String customerName;

    @Schema(description = "家长手机", requiredMode = Schema.RequiredMode.REQUIRED, example = "1730987654")
    private String customerPhone;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27313")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String teacherName;
    private String teacherAvatar;

    @Schema(description = "老师手机", requiredMode = Schema.RequiredMode.REQUIRED, example = "1730987654")
    private String teacherPhone;

    @Schema(description = "上课时长(h) 押金时拆分", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private BigDecimal timeHour;

    @Schema(description = "倍数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private BigDecimal multiple;

    @Schema(description = "计算课时", example = "1")
    private BigDecimal lessonHour;

    @Schema(description = "总课时", example = "1")
    private BigDecimal classHour;

    @Schema(description = "老师-课时单价", example = "31904")
    private BigDecimal teacherPrice;

    @Schema(description = "老师-小时单价", example = "13605")
    private BigDecimal teacherHourPrice;

    @Schema(description = "老师-附加总费用", example = "11")
    private BigDecimal teacherExtraCharge;

    @Schema(description = "老师-总薪资", example = "22")
    private BigDecimal teacherAmount;

    @Schema(description = "家长-课时单价", example = "26922")
    private BigDecimal customerPrice;

    @Schema(description = "家长-扣费金额", example = "22")
    private BigDecimal customerCost;

    @Schema(description = "下次上课时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime nextTime;

    @Schema(description = "课时记录状态", example = "2")
    @DictFormat("als_lesson_record_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer recordStatus;

    @Schema(description = "审核状态", example = "2")
    @DictFormat("als_audit_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer auditStatus;
    
    private String confirmStatus;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", example = "21873")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "随便")
    private String auditRemark;

    @Schema(description = "押金处理方式", example = "1")
    @DictFormat("als_deposit_deal_method") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer dealMethod;

    @Schema(description = "处理人", example = "10901")
    private Long dealUserId;

    @Schema(description = "处理时间")
    private LocalDateTime dealTime;

    @Schema(description = "家长获得课时-上课时长(h)", example = "1")
    private BigDecimal customerGet;

    @Schema(description = "老师获得课时-上课时长(h)", example = "11")
    private BigDecimal teacherGet;

    @Schema(description = "平台获得课时-上课时长(h)")
    private BigDecimal platformGet;

    @Schema(description = "备注时间")
    private LocalDateTime remarkTime;

    @Schema(description = "备注人", example = "27852")
    private Long remarkUserId;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;
    
    /**
     * 提交距离当前时间 几分钟前，几天前
     */
    private String timeSince;

    /**
     * 给家长展示的内容，陪学反思 
     */
    private String showCustomer;
}
