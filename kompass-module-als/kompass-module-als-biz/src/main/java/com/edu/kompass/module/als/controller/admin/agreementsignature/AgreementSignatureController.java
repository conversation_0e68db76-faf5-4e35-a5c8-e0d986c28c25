package com.edu.kompass.module.als.controller.admin.agreementsignature;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

import com.edu.kompass.framework.excel.core.util.ExcelUtils;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.*;

import com.edu.kompass.module.als.controller.admin.agreementsignature.vo.*;
import com.edu.kompass.module.als.dal.dataobject.agreementsignature.AgreementSignatureDO;
import com.edu.kompass.module.als.service.agreementsignature.AgreementSignatureService;

@Tag(name = "管理后台 - 协议签署记录")
@RestController
@RequestMapping("/als/agreement-signature")
@Validated
public class AgreementSignatureController {

    @Resource
    private AgreementSignatureService agreementSignatureService;

    @PostMapping("/create")
    @Operation(summary = "创建协议签署记录")
    @PreAuthorize("@ss.hasPermission('als:agreement-signature:create')")
    public CommonResult<Long> createAgreementSignature(@Valid @RequestBody AgreementSignatureSaveReqVO createReqVO) {
        return success(agreementSignatureService.createAgreementSignature(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新协议签署记录")
    @PreAuthorize("@ss.hasPermission('als:agreement-signature:update')")
    public CommonResult<Boolean> updateAgreementSignature(@Valid @RequestBody AgreementSignatureSaveReqVO updateReqVO) {
        agreementSignatureService.updateAgreementSignature(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除协议签署记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:agreement-signature:delete')")
    public CommonResult<Boolean> deleteAgreementSignature(@RequestParam("id") Long id) {
        agreementSignatureService.deleteAgreementSignature(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得协议签署记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:agreement-signature:query')")
    public CommonResult<AgreementSignatureRespVO> getAgreementSignature(@RequestParam("id") Long id) {
        AgreementSignatureDO agreementSignature = agreementSignatureService.getAgreementSignature(id);
        return success(BeanUtils.toBean(agreementSignature, AgreementSignatureRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得协议签署记录分页")
    @PreAuthorize("@ss.hasPermission('als:agreement-signature:query')")
    public CommonResult<PageResult<AgreementSignatureRespVO>> getAgreementSignaturePage(@Valid AgreementSignaturePageReqVO pageReqVO) {
        PageResult<AgreementSignatureDO> pageResult = agreementSignatureService.getAgreementSignaturePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AgreementSignatureRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出协议签署记录 Excel")
    @PreAuthorize("@ss.hasPermission('als:agreement-signature:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAgreementSignatureExcel(@Valid AgreementSignaturePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AgreementSignatureDO> list = agreementSignatureService.getAgreementSignaturePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "协议签署记录.xls", "数据", AgreementSignatureRespVO.class,
                        BeanUtils.toBean(list, AgreementSignatureRespVO.class));
    }

}