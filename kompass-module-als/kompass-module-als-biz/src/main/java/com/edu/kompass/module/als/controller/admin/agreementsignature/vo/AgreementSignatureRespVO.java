package com.edu.kompass.module.als.controller.admin.agreementsignature.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 协议签署记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AgreementSignatureRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21071")
    @ExcelProperty("主键ID")
    private Long agreementSignatureId;

    @Schema(description = "协议ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16707")
    @ExcelProperty("协议ID")
    private Long agreementId;

    @Schema(description = "协议唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("协议唯一标识")
    private String agreementKey;

    @Schema(description = "签署时的协议版本", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签署时的协议版本")
    private String agreementVersion;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14258")
    @ExcelProperty("用户ID")
    private Long userId;

    @Schema(description = "签名图片URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("签名图片URL")
    private String signatureUrl;

    @Schema(description = "签署IP地址")
    @ExcelProperty("签署IP地址")
    private String ipAddress;

    @Schema(description = "用户代理信息")
    @ExcelProperty("用户代理信息")
    private String userAgent;

    @Schema(description = "签署时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签署时间")
    private LocalDateTime signedAt;

    /**
     * 签署地
     */
    private String signedAddress;

    /**
     * 有效期
     */
    private String periodValidity;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
    
    private String teacherName;
    
    private String idNumber;

}
