package com.edu.kompass.module.als.controller.admin.faq;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.faq.vo.FaqPageReqVO;
import com.edu.kompass.module.als.controller.admin.faq.vo.FaqRespVO;
import com.edu.kompass.module.als.controller.admin.faq.vo.FaqSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.faq.FaqDO;
import com.edu.kompass.module.als.service.faq.FaqService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 常见问题解答")
@RestController
@RequestMapping("/als/faq")
@Validated
public class FaqController {

    @Resource
    private FaqService faqService;

    @PostMapping("/create")
    @Operation(summary = "创建常见问题解答")
    @PreAuthorize("@ss.hasPermission('als:faq:create')")
    public CommonResult<Long> createFaq(@Valid @RequestBody FaqSaveReqVO createReqVO) {
        return success(faqService.createFaq(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新常见问题解答")
    @PreAuthorize("@ss.hasPermission('als:faq:update')")
    public CommonResult<Boolean> updateFaq(@Valid @RequestBody FaqSaveReqVO updateReqVO) {
        faqService.updateFaq(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除常见问题解答")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:faq:delete')")
    public CommonResult<Boolean> deleteFaq(@RequestParam("id") Long id) {
        faqService.deleteFaq(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得常见问题解答")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:faq:query')")
    public CommonResult<FaqRespVO> getFaq(@RequestParam("id") Long id) {
        FaqDO faq = faqService.getFaq(id);
        return success(BeanUtils.toBean(faq, FaqRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得常见问题解答分页")
    @PreAuthorize("@ss.hasPermission('als:faq:query')")
    public CommonResult<PageResult<FaqRespVO>> getFaqPage(@Valid FaqPageReqVO pageReqVO) {
        PageResult<FaqDO> pageResult = faqService.getFaqPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FaqRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出常见问题解答 Excel")
    @PreAuthorize("@ss.hasPermission('als:faq:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportFaqExcel(@Valid FaqPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<FaqDO> list = faqService.getFaqPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "常见问题解答.xls", "数据", FaqRespVO.class,
                        BeanUtils.toBean(list, FaqRespVO.class));
    }

}
