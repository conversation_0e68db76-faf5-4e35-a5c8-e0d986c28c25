package com.edu.kompass.module.als.controller.admin.depositrefundapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 退押申请分页 Request VO")
@Data
@ToString(callSuper = true)
public class DepositRefundApplyReqVO {

    @Schema(description = "申请人", example = "13924")
    private Long teacherId;

    @Schema(description = "课时记录ID", example = "23575")
    private Long lessonHourId;

    @Schema(description = "申请备注", example = "不对")
    private String applyReason;

    @Schema(description = "处理状态", example = "2")
    private Integer dealStatus;

}
