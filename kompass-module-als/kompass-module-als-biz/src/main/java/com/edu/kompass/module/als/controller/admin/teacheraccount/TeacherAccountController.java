package com.edu.kompass.module.als.controller.admin.teacheraccount;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.teacheraccount.vo.TeacherAccountPageReqVO;
import com.edu.kompass.module.als.controller.admin.teacheraccount.vo.TeacherAccountRespVO;
import com.edu.kompass.module.als.controller.admin.teacheraccount.vo.TeacherAccountSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.teacheraccount.TeacherAccountDO;
import com.edu.kompass.module.als.service.teacheraccount.TeacherAccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 老师账户")
@RestController
@RequestMapping("/als/teacher-account")
@Validated
public class TeacherAccountController {

    @Resource
    private TeacherAccountService teacherAccountService;

    @PostMapping("/create")
    @Operation(summary = "创建老师账户")
    @PreAuthorize("@ss.hasPermission('als:teacher-account:create')")
    public CommonResult<Long> createTeacherAccount(@Valid @RequestBody TeacherAccountSaveReqVO createReqVO) {
        return success(teacherAccountService.createTeacherAccount(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新老师账户")
    @PreAuthorize("@ss.hasPermission('als:teacher-account:update')")
    public CommonResult<Boolean> updateTeacherAccount(@Valid @RequestBody TeacherAccountSaveReqVO updateReqVO) {
        teacherAccountService.updateTeacherAccount(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除老师账户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:teacher-account:delete')")
    public CommonResult<Boolean> deleteTeacherAccount(@RequestParam("id") Long id) {
        teacherAccountService.deleteTeacherAccount(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得老师账户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher-account:query')")
    public CommonResult<TeacherAccountRespVO> getTeacherAccount(@RequestParam("id") Long id) {
        TeacherAccountDO teacherAccount = teacherAccountService.getTeacherAccount(id);
        return success(BeanUtils.toBean(teacherAccount, TeacherAccountRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得老师账户分页")
    @PreAuthorize("@ss.hasPermission('als:teacher-account:query')")
    public CommonResult<PageResult<TeacherAccountRespVO>> getTeacherAccountPage(@Valid TeacherAccountPageReqVO pageReqVO) {
        PageResult<TeacherAccountDO> pageResult = teacherAccountService.getTeacherAccountPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TeacherAccountRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出老师账户 Excel")
    @PreAuthorize("@ss.hasPermission('als:teacher-account:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTeacherAccountExcel(@Valid TeacherAccountPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TeacherAccountDO> list = teacherAccountService.getTeacherAccountPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "老师账户.xls", "数据", TeacherAccountRespVO.class,
                        BeanUtils.toBean(list, TeacherAccountRespVO.class));
    }

}
