package com.edu.kompass.module.als.controller.admin.evaluation.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 评价 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EvaluationRespVO {

    @Schema(description = "评价ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15337")
    @ExcelProperty("评价ID")
    private Long evaluationId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17477")
    private Long customerId;

    @Schema(description = "家长姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String customerName;

    @Schema(description = "家长手机", requiredMode = Schema.RequiredMode.REQUIRED, example = "1730987654")
    private String customerPhone;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27313")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String teacherName;

    @Schema(description = "老师手机", requiredMode = Schema.RequiredMode.REQUIRED, example = "1730987654")
    private String teacherPhone;
    
    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19050")
    private Long businessId;

    @Schema(description = "评分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("评分")
    private Integer score;

    @Schema(description = "评分等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("评分等级")
    private String level;

    @Schema(description = "模块", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "模块", converter = DictConvert.class)
    @DictFormat("als_evaluation_module") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer module;

    @Schema(description = "评价内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("评价内容")
    private String content;

    @Schema(description = "处理状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "处理状态", converter = DictConvert.class)
    @DictFormat("als_deal_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer dealStatus;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED, example = "9910")
    @ExcelProperty("处理人")
    private Long dealUserId;

    @Schema(description = "处理人", example = "9910")
    private String dealUserName;

    @Schema(description = "跟踪备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("跟踪备注")
    private String remark;

    @Schema(description = "备注时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("备注时间")
    private LocalDateTime remarkTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
