package com.edu.kompass.module.als.controller.admin.teacherwithdrawapply;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.audit.AuditVo;
import com.edu.kompass.module.als.controller.admin.teacherwithdrawapply.vo.TeacherWithdrawApplyPageReqVO;
import com.edu.kompass.module.als.controller.admin.teacherwithdrawapply.vo.TeacherWithdrawApplyRespVO;
import com.edu.kompass.module.als.controller.admin.teacherwithdrawapply.vo.TeacherWithdrawApplySaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.teacherwithdrawapply.TeacherWithdrawApplyDO;
import com.edu.kompass.module.als.facade.teacher.TeacherWithdrawFacade;
import com.edu.kompass.module.als.service.teacherwithdrawapply.TeacherWithdrawApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 老师提现申请")
@RestController
@RequestMapping("/als/teacher-withdraw-apply")
@Validated
public class TeacherWithdrawApplyController {

    @Resource
    private TeacherWithdrawFacade teacherWithdrawFacade;
    @Resource
    private TeacherWithdrawApplyService teacherWithdrawApplyService;

    @PostMapping("/create")
    @Operation(summary = "创建老师提现申请")
    @PreAuthorize("@ss.hasPermission('als:teacher-withdraw-apply:create')")
    public CommonResult<Long> createTeacherWithdrawApply(@Valid @RequestBody TeacherWithdrawApplySaveReqVO createReqVO) {
        return success(teacherWithdrawFacade.create(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新老师提现申请")
    @PreAuthorize("@ss.hasPermission('als:teacher-withdraw-apply:update')")
    public CommonResult<Boolean> updateTeacherWithdrawApply(@Valid @RequestBody TeacherWithdrawApplySaveReqVO updateReqVO) {
        teacherWithdrawApplyService.updateTeacherWithdrawApplyValid(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除老师提现申请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:teacher-withdraw-apply:delete')")
    public CommonResult<Boolean> deleteTeacherWithdrawApply(@RequestParam("id") Long id) {
        teacherWithdrawApplyService.deleteTeacherWithdrawApply(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得老师提现申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher-withdraw-apply:query')")
    public CommonResult<TeacherWithdrawApplyRespVO> getTeacherWithdrawApply(@RequestParam("id") Long id) {
        TeacherWithdrawApplyDO teacherWithdrawApply = teacherWithdrawApplyService.getTeacherWithdrawApply(id);
        return success(BeanUtils.toBean(teacherWithdrawApply, TeacherWithdrawApplyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得老师提现申请分页")
    @PreAuthorize("@ss.hasPermission('als:teacher-withdraw-apply:query')")
    public CommonResult<PageResult<TeacherWithdrawApplyRespVO>> getTeacherWithdrawApplyPage(@Valid TeacherWithdrawApplyPageReqVO pageReqVO) {
        PageResult<TeacherWithdrawApplyRespVO> pageResult = teacherWithdrawFacade.getTeacherWithdrawApplyPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出老师提现申请 Excel")
    @PreAuthorize("@ss.hasPermission('als:teacher-withdraw-apply:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTeacherWithdrawApplyExcel(@Valid TeacherWithdrawApplyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TeacherWithdrawApplyDO> list = teacherWithdrawApplyService.getTeacherWithdrawApplyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "老师提现申请.xls", "数据", TeacherWithdrawApplyRespVO.class,
                        BeanUtils.toBean(list, TeacherWithdrawApplyRespVO.class));
    }

    @PutMapping("/audit")
    @Operation(summary = "审核")
    @PreAuthorize("@ss.hasPermission('als:bind:update')")
    public CommonResult<Boolean> audit(@Valid @RequestBody AuditVo auditVo) {
        teacherWithdrawFacade.audit(auditVo);
        return success(true);
    }

}
