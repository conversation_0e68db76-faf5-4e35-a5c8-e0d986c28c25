package com.edu.kompass.module.als.controller.admin.lessonplan.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 陪学计划 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LessonPlanRespVO {

    @Schema(description = "陪学计划ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25582")
    @ExcelProperty("陪学计划ID")
    private Long lessonPlanId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3150")
    @ExcelProperty("订单ID")
    private Long orderId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3571")
    @ExcelProperty("家长ID")
    private Long customerId;

    @Schema(description = "家长姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("家长姓名")
    private String customerName;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13022")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("老师姓名")
    private String teacherName;

    @Schema(description = "计划课时数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("计划课时数")
    private Integer planHour;

    @Schema(description = "已完成课时数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("已完成课时数")
    private Integer finishedHour;

    @Schema(description = "计划开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("计划开始时间")
    private LocalDateTime startTime;

    @Schema(description = "计划结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("计划结束时间")
    private LocalDateTime endTime;

    @Schema(description = "总体目标", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总体目标")
    private String planTarget;

    @Schema(description = "家长期望", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("家长期望")
    private String customerExpect;

    @Schema(description = "Q1阶段目标", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("Q1阶段目标")
    private String stageQ1Target;

    @Schema(description = "Q2阶段目标", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("Q2阶段目标")
    private String stageQ2Target;

    @Schema(description = "Q3阶段目标", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("Q3阶段目标")
    private String stageQ3Target;

    @Schema(description = "Q4阶段目标", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("Q4阶段目标")
    private String stageQ4Target;

    @Schema(description = "Q1打分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("Q1打分")
    private Integer stageQ1Score;

    @Schema(description = "Q2打分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("Q2打分")
    private Integer stageQ2Score;

    @Schema(description = "Q3打分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("Q3打分")
    private Integer stageQ3Score;

    @Schema(description = "Q4打分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("Q4打分")
    private Integer stageQ4Score;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat("als_audit_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer planAuditStatus;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime planAuditTime;

    @Schema(description = "审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "10338")
    @ExcelProperty("审核人")
    private Long planAuditUserId;

    @Schema(description = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("审核备注")
    private String planAuditRemark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
