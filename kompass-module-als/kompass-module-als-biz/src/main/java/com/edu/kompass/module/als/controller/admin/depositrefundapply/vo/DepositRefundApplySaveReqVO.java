package com.edu.kompass.module.als.controller.admin.depositrefundapply.vo;

import com.edu.kompass.framework.mybatis.core.dataobject.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 退押申请新增/修改 Request VO")
@Data
public class DepositRefundApplySaveReqVO extends BaseVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4377")
    private Long depositRefundApplyId;

    @Schema(description = "申请人", requiredMode = Schema.RequiredMode.REQUIRED, example = "13924")
    @NotNull(message = "申请人不能为空")
    private Long teacherId;

    @Schema(description = "课时记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23575")
    @NotNull(message = "课时记录ID不能为空")
    private Long lessonHourId;

    @Schema(description = "申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "申请备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "不对")
    @NotEmpty(message = "申请备注不能为空")
    private String applyReason;

    @Schema(description = "处理状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "处理状态不能为空")
    private Integer dealStatus;

}
