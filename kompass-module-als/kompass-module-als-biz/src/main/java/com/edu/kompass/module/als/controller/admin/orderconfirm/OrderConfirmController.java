package com.edu.kompass.module.als.controller.admin.orderconfirm;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.orderconfirm.vo.OrderConfirmPageReqVO;
import com.edu.kompass.module.als.controller.admin.orderconfirm.vo.OrderConfirmRespVO;
import com.edu.kompass.module.als.controller.admin.orderconfirm.vo.OrderConfirmSaveReqVO;
import com.edu.kompass.module.als.controller.admin.orderconfirm.vo.RejectReqVO;
import com.edu.kompass.module.als.dal.dataobject.orderconfirm.OrderConfirmDO;
import com.edu.kompass.module.als.dal.dataobject.orderconfirm.OrderConfirmPageDO;
import com.edu.kompass.module.als.facade.order.OrderConfirmFacade;
import com.edu.kompass.module.als.service.orderconfirm.OrderConfirmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 接单确认")
@RestController
@RequestMapping("/als/order-confirm")
@Validated
public class OrderConfirmController {

    @Resource
    private OrderConfirmService orderConfirmService;

    @Resource
    private OrderConfirmFacade orderConfirmFacade;

    @GetMapping("/confirm")
    @Operation(summary = "确认接单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:order-confirm:update')")
    public CommonResult<Boolean> confirm(@RequestParam("id") Long id) {
        orderConfirmFacade.confirm(id);
        return success(true);
    }

    @PostMapping("/reject")
    @Operation(summary = "拒绝接单")
    @PreAuthorize("@ss.hasPermission('als:order-confirm:update')")
    public CommonResult<Boolean> reject(@Valid @RequestBody RejectReqVO rejectReqVO) {
        orderConfirmFacade.reject(rejectReqVO);
        return success(true);
    }

    @PostMapping("/create")
    @Operation(summary = "创建接单确认")
    @PreAuthorize("@ss.hasPermission('als:order-confirm:create')")
    public CommonResult<Long> createOrderConfirm(@Valid @RequestBody OrderConfirmSaveReqVO createReqVO) {
        return success(orderConfirmService.createOrderConfirm(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新接单确认")
    @PreAuthorize("@ss.hasPermission('als:order-confirm:update')")
    public CommonResult<Boolean> updateOrderConfirm(@Valid @RequestBody OrderConfirmSaveReqVO updateReqVO) {
        orderConfirmService.updateOrderConfirm(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除接单确认")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:order-confirm:delete')")
    public CommonResult<Boolean> deleteOrderConfirm(@RequestParam("id") Long id) {
        orderConfirmService.deleteOrderConfirm(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得接单确认")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:order-confirm:query')")
    public CommonResult<OrderConfirmRespVO> getOrderConfirm(@RequestParam("id") Long id) {
        OrderConfirmDO orderConfirm = orderConfirmService.getOrderConfirm(id);
        return success(BeanUtils.toBean(orderConfirm, OrderConfirmRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得接单确认分页")
    @PreAuthorize("@ss.hasPermission('als:order-confirm:query')")
    public CommonResult<PageResult<OrderConfirmRespVO>> getOrderConfirmPage(@Valid OrderConfirmPageReqVO pageReqVO) {
        PageResult<OrderConfirmRespVO> pageResult = orderConfirmFacade.getOrderConfirmPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出接单确认 Excel")
    @PreAuthorize("@ss.hasPermission('als:order-confirm:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOrderConfirmExcel(@Valid OrderConfirmPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OrderConfirmPageDO> list = orderConfirmService.getOrderConfirmPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "接单确认.xls", "数据", OrderConfirmRespVO.class,
                        BeanUtils.toBean(list, OrderConfirmRespVO.class));
    }

}
