package com.edu.kompass.module.als.controller.admin.withdrawapply;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.audit.AuditVo;
import com.edu.kompass.module.als.controller.admin.withdrawapply.vo.WithdrawApplyPageReqVO;
import com.edu.kompass.module.als.controller.admin.withdrawapply.vo.WithdrawApplyRespVO;
import com.edu.kompass.module.als.controller.admin.withdrawapply.vo.WithdrawApplySaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.withdrawapply.WithdrawApplyDO;
import com.edu.kompass.module.als.facade.teacher.WithdrawFacade;
import com.edu.kompass.module.als.service.withdrawapply.WithdrawApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 提现申请")
@RestController
@RequestMapping("/als/withdraw-apply")
@Validated
public class WithdrawApplyController {

    @Resource
    private WithdrawApplyService withdrawApplyService;
    
    @Resource
    private WithdrawFacade withdrawFacade;  

    @PostMapping("/create")
    @Operation(summary = "创建提现申请")
    @PreAuthorize("@ss.hasPermission('als:withdraw-apply:create')")
    public CommonResult<Long> createWithdrawApply(@Valid @RequestBody WithdrawApplySaveReqVO createReqVO) {
        return success(withdrawApplyService.createWithdrawApply(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新提现申请")
    @PreAuthorize("@ss.hasPermission('als:withdraw-apply:update')")
    public CommonResult<Boolean> updateWithdrawApply(@Valid @RequestBody WithdrawApplySaveReqVO updateReqVO) {
        withdrawApplyService.updateWithdrawApply(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除提现申请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:withdraw-apply:delete')")
    public CommonResult<Boolean> deleteWithdrawApply(@RequestParam("id") Long id) {
        withdrawApplyService.deleteWithdrawApply(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得提现申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:withdraw-apply:query')")
    public CommonResult<WithdrawApplyRespVO> getWithdrawApply(@RequestParam("id") Long id) {
        WithdrawApplyDO withdrawApply = withdrawApplyService.getWithdrawApply(id);
        return success(BeanUtils.toBean(withdrawApply, WithdrawApplyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得提现申请分页")
    @PreAuthorize("@ss.hasPermission('als:withdraw-apply:query')")
    public CommonResult<PageResult<WithdrawApplyRespVO>> getWithdrawApplyPage(@Valid WithdrawApplyPageReqVO pageReqVO) {
        PageResult<WithdrawApplyDO> pageResult = withdrawApplyService.getWithdrawApplyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WithdrawApplyRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出提现申请 Excel")
    @PreAuthorize("@ss.hasPermission('als:withdraw-apply:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWithdrawApplyExcel(@Valid WithdrawApplyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WithdrawApplyDO> list = withdrawApplyService.getWithdrawApplyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "提现申请.xls", "数据", WithdrawApplyRespVO.class,
                        BeanUtils.toBean(list, WithdrawApplyRespVO.class));
    }

    @PutMapping("/audit")
    @Operation(summary = "审核")
    @PreAuthorize("@ss.hasPermission('als:bind:update')")
    public CommonResult<Boolean> audit(@Valid @RequestBody AuditVo auditVo) {
        withdrawFacade.audit(auditVo);
        return success(true);
    }

    @DeleteMapping("/pay")
    @Operation(summary = "付款")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:bind:update')")
    public CommonResult<Boolean> pay(@RequestParam("id") Long id) {
        withdrawFacade.pay(id);
        return success(true);
    }

}
