package com.edu.kompass.module.als.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 陪学订单修改 Request VO")
@Data
public class OrderUpdateReqVO {

    @Schema(description = "主键", example = "1395")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "订单编号", example = "PX20240903")
    private String orderNo;
    
    @Schema(description = "订单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer orderStatus;

    @Schema(description = "订单类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer orderType;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27437")
    private Long customerId;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "17302589292")
    private String customerPhone;

    @Schema(description = "区域", requiredMode = Schema.RequiredMode.REQUIRED, example = "784")
    private Integer orderAreaId;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "江苏省南京市XX小区")
    private String orderAddress;

    @Schema(description = "是否暂停接单", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer isSuspend;

    @Schema(description = "是否已建群", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer isGroupChat;

    @Schema(description = "跟踪备注补充", example = "XXXX")
    private String orderRemarkExtra;

    @Schema(description = "订单备注", example = "这个家长很相信我们")
    private String orderRemark;

    @Schema(description = "当前负责人", example = "1")
    private Integer headCurrent;

    @Schema(description = "原家长陪学需求", example = "【到家陪学】")
    private String orgNeedsContent;

    @Schema(description = "老师性别要求", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer requireSex;

    @Schema(description = "老师能力硬性要求", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private List<String> hardRequireAbility;

    @Schema(description = "老师能力要求补充", example = "要求教育专业")
    private String requireAbilityExtra;

    @Schema(description = "需求标签", requiredMode = Schema.RequiredMode.REQUIRED, example = "1,2")
    private List<String> needsTags;

    @Schema(description = "需求侧重点", example = "1,2")
    private List<String> needsFocusTags;

    @Schema(description = "陪学要求", requiredMode = Schema.RequiredMode.REQUIRED, example = "【到家陪学】")
    private String demandContent;

    @Schema(description = "周次", example = "1")
    private Integer timesWeek;

    @Schema(description = "是否是周末订单", example = "1")
    private Integer isOnWeekend;

    @Schema(description = "陪学时间范围", requiredMode = Schema.RequiredMode.REQUIRED, example = "1,2")
    private List<Integer> timeRange;

    @Schema(description = "跟踪时间", example = "2024-07-30 22:55:45")
    private LocalDateTime trackingTime;

    @Schema(description = "跟踪备注标签", example = "[1,2,3]")
    private List<Integer> trackingRemarkTags;

    @Schema(description = "体验时间", example = "2024-07-30 22:55:59")
    private LocalDateTime expTime;

    @Schema(description = "是否确认体验时间", example = "1")
    private Integer isConfirmExpTime;

    @Schema(description = "孩子性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer kidSex;

    @Schema(description = "孩子年级阶段", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    private Integer kidStage;

    @Schema(description = "沟通前提条件", example = "1,2")
    private List<String> communicatePre;

    @Schema(description = "沟通结果", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer communicateResult;

    @Schema(description = "为什么需要陪学", example = "家里需要")
    private String whyNeed;

    @Schema(description = "主要教育者", example = "妈妈")
    private String primaryEducator;

    @Schema(description = "孩子称呼", requiredMode = Schema.RequiredMode.REQUIRED, example = "李娃")
    private String kidNickName;

    @Schema(description = "家长与孩子关系", requiredMode = Schema.RequiredMode.REQUIRED, example = "母子")
    private String relationship;

    @Schema(description = "学校名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String schoolName;

    @Schema(description = "学校性质", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer schoolNature;

    @Schema(description = "大致排名", example = "1")
    private Integer ranking;

    @Schema(description = "孩子性格", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String kidChr;

    @Schema(description = "孩子性格补充", example = "无")
    private String kidChrExtra;

    @Schema(description = "孩子兴趣", example = "1")
    private String kidInt;

    @Schema(description = "孩子兴趣补充", example = "无")
    private String kidIntExtra;

    @Schema(description = "邀请人", example = "56")
    private Integer inviterId;

    @Schema(description = "订单来源渠道", example = "1")
    private Integer sourceChannel;

    @Schema(description = "被换老师ID", example = "1")
    private Long changedTeacherId;

    @Schema(description = "被换原因标签", example = "1")
    private Integer changedReasonTags;

    @Schema(description = "被换具体原因", example = "1")
    private String changedReason;

}
