package com.edu.kompass.module.als.controller.admin.orderconfirm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class RejectReqVO {

    @NotNull(message = "确认接单ID不能为空")
    private Long orderConfirmId;

    /**
     * 不合适原因
     */
    @Schema(description = "拒绝原因ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "拒绝原因不能为空")
    private Integer rejectReasonId;

    /**
     * 自定义不合适原因
     */
    private String customReason;

}
