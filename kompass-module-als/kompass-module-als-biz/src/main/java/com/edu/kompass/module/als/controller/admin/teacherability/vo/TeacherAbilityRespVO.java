package com.edu.kompass.module.als.controller.admin.teacherability.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 老师能力 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TeacherAbilityRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19666")
    @ExcelProperty("主键")
    private Long teacherAbilityId;

    @Schema(description = "外语", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "外语", converter = DictConvert.class)
    @DictFormat("als_foreign_language")
    private List<Integer> foreignLanguage;

    @Schema(description = "外语口语能力", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "外语口语能力", converter = DictConvert.class)
    @DictFormat("als_teacher_ability_spoken")
    private List<Integer> foreignLanguageSpoken;

    @Schema(description = "外语证明", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("外语证明")
    private String foreignCertificate;

    @Schema(description = "四级分数")
    @ExcelProperty("四级分数")
    private Integer gradeFour;

    @Schema(description = "六级分数")
    @ExcelProperty("六级分数")
    private Integer gradeSix;

    @Schema(description = "高考英语")
    @ExcelProperty("高考英语")
    private Integer englishScore;

    @Schema(description = "雅思")
    @ExcelProperty("雅思")
    private BigDecimal ieltsScore;

    @Schema(description = "托福")
    private BigDecimal toeflScore;

    @Schema(description = "钢琴等级")
    private String pianoLevel;

    @Schema(description = "钢琴证书颁证方")
    private String pianoCertificateIssuer;

    @Schema(description = "其他技能证书及获奖情况")
    private String otherCertificate;

    @Schema(description = "教师资格证URL")
    private String teacherCertificateImgUrl;

    @Schema(description = "在校获奖情况", requiredMode = Schema.RequiredMode.REQUIRED)
    @DictFormat("als_school_awards")
    private List<Integer> schoolAwards;

    @Schema(description = "在校获奖情况级别", requiredMode = Schema.RequiredMode.REQUIRED)
    @DictFormat("als_school_awards_level")
    private List<Integer> schoolAwardsLevel;

    @Schema(description = "补充奖项", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("补充奖项")
    private String schoolAwardsExtra;

    @Schema(description = "特长", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "特长", converter = DictConvert.class)
    @DictFormat("als_forte") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> forte;

    @Schema(description = "其他特长", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("其他特长")
    private String forteExtra;

    @Schema(description = "家教经历", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("家教经历")
    private String experience;

    @Schema(description = "教学方法", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("教学方法")
    private String teachingMethod;

    @Schema(description = "作业辅导科目擅长排序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("作业辅导科目擅长排序")
    private List<String> teachScopeRank;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "英语口语")
    private String englishSpoken;
}
