package com.edu.kompass.module.als.controller.admin.teacherinterview;

import cn.hutool.core.bean.BeanUtil;
import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.framework.idempotent.core.annotation.Idempotent;
import com.edu.kompass.framework.web.core.util.WebFrameworkUtils;
import com.edu.kompass.module.als.controller.admin.teacherinterview.vo.TeacherInterviewPageReqVO;
import com.edu.kompass.module.als.controller.admin.teacherinterview.vo.TeacherInterviewRespVO;
import com.edu.kompass.module.als.controller.admin.teacherinterview.vo.TeacherInterviewSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.teacherinterview.TeacherInterviewDO;
import com.edu.kompass.module.als.dal.dataobject.teacherinterview.TeacherInterviewDefineDO;
import com.edu.kompass.module.als.service.teacherinterview.TeacherInterviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 老师面试")
@RestController
@RequestMapping("/als/teacher-interview")
@Validated
public class TeacherInterviewController {

    @Resource
    private TeacherInterviewService teacherInterviewService;

    @PostMapping("/create")
    @Operation(summary = "创建老师面试")
    @PreAuthorize("@ss.hasPermission('als:teacher-interview:create')")
    public CommonResult<Long> createTeacherInterview(@Valid @RequestBody TeacherInterviewSaveReqVO createReqVO) {
        return success(teacherInterviewService.createTeacherInterview(createReqVO));
    }

    @PostMapping("/reservation")
    @Operation(summary = "预约面试")
    @PreAuthorize("@ss.hasPermission('als:teacher-interview:create')")
    @Idempotent(timeout = 10, message = "提交中，请勿重复提交")
    public CommonResult<Long> reservation( @RequestBody TeacherInterviewSaveReqVO createReqVO) {
        return success(teacherInterviewService.reservation(createReqVO));
    }
    
    @PutMapping("/update")
    @Operation(summary = "更新老师面试")
    @PreAuthorize("@ss.hasPermission('als:teacher-interview:update')")
    public CommonResult<Boolean> updateTeacherInterview(@Valid @RequestBody TeacherInterviewSaveReqVO updateReqVO) {
        teacherInterviewService.updateTeacherInterview(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除老师面试")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:teacher-interview:delete')")
    public CommonResult<Boolean> deleteTeacherInterview(@RequestParam("id") Long id) {
        teacherInterviewService.deleteTeacherInterview(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得老师面试")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher-interview:query')")
    public CommonResult<TeacherInterviewRespVO> getTeacherInterview(@RequestParam("id") Long teacherId) {
        TeacherInterviewDO teacherInterview = teacherInterviewService.getTeacherInterviewByTeacherId(teacherId);
        if (Objects.isNull(teacherInterview)){
            return CommonResult.success(new TeacherInterviewRespVO());
        }
        TeacherInterviewRespVO result = BeanUtils.toBean(teacherInterview, TeacherInterviewRespVO.class);
        result.setInterviewTime(LocalDateTime.now());
        result.setInterviewer(teacherInterview.getInterviewer());
        result.setTeacherId(teacherId);
        return success(result);
    }

    @GetMapping("/page")
    @Operation(summary = "面试分页查询")
    @PreAuthorize("@ss.hasPermission('als:teacher-interview:query')")
    public CommonResult<PageResult<TeacherInterviewRespVO>> getTeacherInterviewPage(@Valid TeacherInterviewPageReqVO pageReqVO) {
        PageResult<TeacherInterviewDefineDO> pageResult = teacherInterviewService.getTeacherInterviewPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TeacherInterviewRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出老师面试 Excel")
    @PreAuthorize("@ss.hasPermission('als:teacher-interview:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTeacherInterviewExcel(@Valid TeacherInterviewPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TeacherInterviewDefineDO> list1 = teacherInterviewService.getTeacherInterviewPage(pageReqVO).getList();
        List<TeacherInterviewDO> list = BeanUtil.copyToList(list1, TeacherInterviewDO.class);
        // 导出 Excel
        ExcelUtils.write(response, "老师面试.xls", "数据", TeacherInterviewRespVO.class,
                        BeanUtils.toBean(list, TeacherInterviewRespVO.class));
    }

}
