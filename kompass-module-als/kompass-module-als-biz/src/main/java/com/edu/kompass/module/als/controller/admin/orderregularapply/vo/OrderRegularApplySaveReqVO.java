package com.edu.kompass.module.als.controller.admin.orderregularapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 正式课申请新增/修改 Request VO")
@Data
public class OrderRegularApplySaveReqVO {

    @Schema(description = "正式课申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5357")
    private Long orderRegularApplyId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "22856")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11830")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12397")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "体验课反馈简述", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "体验课反馈简述不能为空")
    private String feedbackDesc;

    @Schema(description = "体验课反馈表", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "体验课反馈表不能为空")
    private String feedbackUrl;

    @Schema(description = "正式课陪学计划表", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "正式课陪学计划表不能为空")
    private String planUrl;

    @Schema(description = "陪学计划ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25424")
    @NotNull(message = "陪学计划ID不能为空")
    private Long planLessonId;

    @Schema(description = "陪学公约", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "陪学公约不能为空")
    private String agreedUrl;

    @Schema(description = "反馈表审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "反馈表审核状态不能为空")
    private Integer feedbackAuditStatus;

    @Schema(description = "反馈表审核时间")
    private LocalDateTime feedbackAuditTime;

    @Schema(description = "反馈表审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "2796")
    @NotNull(message = "反馈表审核人不能为空")
    private Long feedbackAuditUserId;

    @Schema(description = "反馈表审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "反馈表审核备注不能为空")
    private String feedbackAuditRemark;

    @Schema(description = "来自家长的评价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "来自家长的评价不能为空")
    private Integer customerEvaluation;

    @Schema(description = "正式课陪学计划审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "正式课陪学计划审核状态不能为空")
    private Integer planAuditStatus;

    @Schema(description = "正式课陪学计划审核时间")
    private LocalDateTime planAuditTime;

    @Schema(description = "正式课陪学计划审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "3878")
    @NotNull(message = "正式课陪学计划审核人不能为空")
    private Long planAuditUserId;

    @Schema(description = "正式课陪学计划审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @NotEmpty(message = "正式课陪学计划审核备注不能为空")
    private String planAuditRemark;

}
