package com.edu.kompass.module.als.controller.admin.leaveapply.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 请假申请列表分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeaveApplyPageReqVO extends PageParam {

    @Schema(description = "请假申请ID", example = "13415")
    private Long leaveApplyId;

    @Schema(description = "课时记录ID", example = "3633")
    private Long orderRecordId;

    @Schema(description = "订单ID", example = "1648")
    private Long orderId;

    @Schema(description = "家长ID", example = "12836")
    private Long customerId;

    @Schema(description = "老师ID", example = "12929")
    private Long teacherId;

    @Schema(description = "陪学阶段")
    private Integer stage;

    @Schema(description = "请假方")
    private Integer whoLeave;

    @Schema(description = "原定上课时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] planTime;

    @Schema(description = "调整后上课时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] newTime;

    @Schema(description = "申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] applyTime;

    @Schema(description = "申请备注", example = "随便")
    private String applyRemark;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "审核人", example = "15912")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "你说的对")
    private String auditRemark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
