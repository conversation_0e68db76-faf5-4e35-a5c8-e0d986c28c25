package com.edu.kompass.module.als.controller.admin.bind;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.framework.web.core.util.WebFrameworkUtils;
import com.edu.kompass.module.als.controller.admin.audit.AuditVo;
import com.edu.kompass.module.als.controller.admin.bind.vo.BindPageReqVO;
import com.edu.kompass.module.als.controller.admin.bind.vo.BindRespVO;
import com.edu.kompass.module.als.controller.admin.bind.vo.BindSaveReqVO;
import com.edu.kompass.module.als.controller.admin.bind.vo.UnbindAuditReqVO;
import com.edu.kompass.module.als.dal.dataobject.bind.BindDO;
import com.edu.kompass.module.als.facade.customer.CustomerBindFacade;
import com.edu.kompass.module.als.service.bind.BindService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 绑定")
@RestController
@RequestMapping("/als/bind")
@Validated
public class BindController {

    @Resource
    private BindService bindService;

    @Resource
    private CustomerBindFacade customerBindFacade;

    @PostMapping("/create")
    @Operation(summary = "创建绑定")
    @PreAuthorize("@ss.hasPermission('als:bind:create')")
    public CommonResult<Long> createBind(@Valid @RequestBody BindSaveReqVO createReqVO) {
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        createReqVO.setBindUserId(loginUserId);
        Long bind = customerBindFacade.createBind(createReqVO);
        return success(bind);
    }

    @GetMapping("/unbind")
    @Operation(summary = "解除绑定")
    @PreAuthorize("@ss.hasPermission('als:bind:update')")
    public CommonResult<Boolean> unbind(@RequestParam("id") Long id) {
        customerBindFacade.unbind(id);
        return success(true);
    }

    @PutMapping("/unbindAudit")
    @Operation(summary = "解除绑定审核")
    @PreAuthorize("@ss.hasPermission('als:bind:update')")
    public CommonResult<Boolean> unbindAudit(@Valid @RequestBody AuditVo auditVo) {
        customerBindFacade.unbindAudit(auditVo);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新绑定")
    @PreAuthorize("@ss.hasPermission('als:bind:update')")
    public CommonResult<Boolean> updateBind(@Valid @RequestBody BindSaveReqVO updateReqVO) {
        bindService.updateBind(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除绑定")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:bind:delete')")
    public CommonResult<Boolean> deleteBind(@RequestParam("id") Long id) {
        bindService.deleteBind(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得绑定")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:bind:query')")
    public CommonResult<BindRespVO> getBind(@RequestParam("id") Long id) {
        BindDO bind = bindService.getBind(id);
        return success(BeanUtils.toBean(bind, BindRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得绑定分页")
    @PreAuthorize("@ss.hasPermission('als:bind:query')")
    public CommonResult<PageResult<BindRespVO>> getBindPage(@Valid BindPageReqVO pageReqVO) {
        PageResult<BindRespVO> pageResult = customerBindFacade.getBindPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出绑定 Excel")
    @PreAuthorize("@ss.hasPermission('als:bind:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportBindExcel(@Valid BindPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BindDO> list = bindService.getBindPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "绑定.xls", "数据", BindRespVO.class,
                        BeanUtils.toBean(list, BindRespVO.class));
    }

}
