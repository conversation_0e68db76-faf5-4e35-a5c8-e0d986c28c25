package com.edu.kompass.module.als.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class OrderChangeTeacherReqVo {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @NotNull(message = "被换原因标签不能为空")
    private Integer changedReasonTags;

    @NotBlank(message = "被换具体原因")
    private String changedReason;

    @NotNull(message = "更换老师ID不能为空")
    private Long changedTeacherId;   

    @Schema(description = "好评")
    private String goodComment;

    @Schema(description = "差评")
    private String badComment;
}
