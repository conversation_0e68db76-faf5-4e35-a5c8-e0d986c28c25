package com.edu.kompass.module.als.controller.app.lessonhour;

import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.web.core.util.WebFrameworkUtils;
import com.edu.kompass.module.als.controller.admin.audit.AuditVo;
import com.edu.kompass.module.als.controller.app.lessonhour.vo.AppLessonHourPageReqVO;
import com.edu.kompass.module.als.controller.app.lessonhour.vo.AppLessonHourPosterVO;
import com.edu.kompass.module.als.controller.app.lessonhour.vo.AppLessonHourRespVO;
import com.edu.kompass.module.als.controller.app.lessonhour.vo.AppTeacherCustomerVo;
import com.edu.kompass.module.als.enums.AuditStatusEnum;
import com.edu.kompass.module.als.facade.app.teacher.AppLessonHourFacade;
import com.edu.kompass.module.als.facade.teacher.LessonHourFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "app - 课时记录")
@RestController
@RequestMapping("/als/lesson-hour")
@Validated
public class AppLessonHourController {

    @Resource
    private AppLessonHourFacade appLessonHourFacade;
    @Resource
    private LessonHourFacade lessonHourFacade;

    @GetMapping("/page")
    @Operation(summary = "课时记录分页列表")
    public CommonResult<PageResult<AppLessonHourRespVO>> getLessonHourPage(@Valid AppLessonHourPageReqVO pageReqVO) {
        PageResult<AppLessonHourRespVO> pageResult = appLessonHourFacade.getLessonHourPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/get")
    @Operation(summary = "课时记录详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppLessonHourRespVO> getLessonHour(@RequestParam("id") Long id) {
        AppLessonHourRespVO lessonHour = appLessonHourFacade.getLessonHour(id);
        return success(lessonHour);
    }

    @GetMapping("/confirmNum")
    @Operation(summary = "待确认数量")
    public CommonResult<Integer> waitConfirmNum() {
        Long customerMemberId = WebFrameworkUtils.getLoginUserId();
        AppTeacherCustomerVo teacherCustomerVo = new AppTeacherCustomerVo();
        teacherCustomerVo.setCustomerMemberId(customerMemberId);
        Integer confirmNum = appLessonHourFacade.getConfirmNum(teacherCustomerVo);
        return success(confirmNum);
    }

    @GetMapping("/confirm")
    @Operation(summary = "确认")
    public CommonResult<Boolean> confirm(@RequestParam("id") Long id) {
        AuditVo auditVo = new AuditVo();
        auditVo.setId(id);
        auditVo.setAuditStatus(AuditStatusEnum.APPROVED.getCode());
        lessonHourFacade.audit(auditVo);
        return success(true);
    }

    @GetMapping("/poster")
    @Operation(summary = "课时记录海报")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppLessonHourPosterVO> getPoster(@RequestParam("id") Long id) {
        AppLessonHourPosterVO posterVO = appLessonHourFacade.getPoster(id);
        return success(posterVO);
    }
}
