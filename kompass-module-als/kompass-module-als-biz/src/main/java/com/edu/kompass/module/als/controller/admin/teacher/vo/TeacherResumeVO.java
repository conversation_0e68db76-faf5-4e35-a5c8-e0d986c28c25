package com.edu.kompass.module.als.controller.admin.teacher.vo;

import com.edu.kompass.module.als.controller.admin.order.vo.ExceptionSummaryVo;
import com.edu.kompass.module.als.controller.admin.order.vo.ServiceSummaryVo;
import com.edu.kompass.module.als.controller.admin.order.vo.TeacherOrderCountVo;
import com.edu.kompass.module.als.controller.admin.teacherability.vo.TeacherAbilityRespVO;
import com.edu.kompass.module.als.controller.admin.teacherinterview.vo.TeacherInterviewRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 老师简历类 Response VO")
@Data
public class TeacherResumeVO {

    @Schema(description = "老师详情")
    private TeacherRespVO baseInfo;

    @Schema(description = "老师能力")
    private TeacherAbilityRespVO abilityInfo;

    @Schema(description = "面试")
    private TeacherInterviewRespVO interviewInfo;

    @Schema(description = "体验课情况")
    private TeacherOrderCountVo orderInfo;

    @Schema(description = "异常汇总")
    private List<ExceptionSummaryVo> exceptionSummaryList;

    @Schema(description = "异常汇总")
    private List<ServiceSummaryVo> serviceSummaryList;
}
