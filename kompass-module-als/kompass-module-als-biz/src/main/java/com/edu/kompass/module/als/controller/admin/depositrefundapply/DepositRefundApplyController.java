package com.edu.kompass.module.als.controller.admin.depositrefundapply;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.framework.security.core.util.SecurityFrameworkUtils;
import com.edu.kompass.module.als.controller.admin.depositrefundapply.vo.DepositRefundApplyPageReqVO;
import com.edu.kompass.module.als.controller.admin.depositrefundapply.vo.DepositRefundApplyRespVO;
import com.edu.kompass.module.als.controller.admin.depositrefundapply.vo.DepositRefundApplySaveReqVO;
import com.edu.kompass.module.als.controller.admin.depositrefundapply.vo.DepositRefundDealReqVO;
import com.edu.kompass.module.als.dal.dataobject.depositrefundapply.DepositRefundApplyDO;
import com.edu.kompass.module.als.facade.teacher.LessonHourFacade;
import com.edu.kompass.module.als.service.depositrefundapply.DepositRefundApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 退押申请")
@RestController
@RequestMapping("/als/deposit-refund-apply")
@Validated
public class DepositRefundApplyController {

    @Resource
    private DepositRefundApplyService depositRefundApplyService;

    @Resource
    private LessonHourFacade lessonHourFacade;

    @PostMapping("/create")
    @Operation(summary = "创建退押申请")
    @PreAuthorize("@ss.hasPermission('als:deposit-refund-apply:create')")
    public CommonResult<Long> createDepositRefundApply(@Valid @RequestBody DepositRefundApplySaveReqVO createReqVO) {
        return success(depositRefundApplyService.createDepositRefundApply(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新退押申请")
    @PreAuthorize("@ss.hasPermission('als:deposit-refund-apply:update')")
    public CommonResult<Boolean> updateDepositRefundApply(@Valid @RequestBody DepositRefundApplySaveReqVO updateReqVO) {
        depositRefundApplyService.updateDepositRefundApply(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除退押申请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:deposit-refund-apply:delete')")
    public CommonResult<Boolean> deleteDepositRefundApply(@RequestParam("id") Long id) {
        depositRefundApplyService.deleteDepositRefundApply(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得退押申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:deposit-refund-apply:query')")
    public CommonResult<DepositRefundApplyRespVO> getDepositRefundApply(@RequestParam("id") Long id) {
        DepositRefundApplyDO depositRefundApply = depositRefundApplyService.getDepositRefundApply(id);
        return success(BeanUtils.toBean(depositRefundApply, DepositRefundApplyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得退押申请分页")
    @PreAuthorize("@ss.hasPermission('als:deposit-refund-apply:query')")
    public CommonResult<PageResult<DepositRefundApplyRespVO>> getDepositRefundApplyPage(@Valid DepositRefundApplyPageReqVO pageReqVO) {
        PageResult<DepositRefundApplyDO> pageResult = depositRefundApplyService.getDepositRefundApplyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DepositRefundApplyRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出退押申请 Excel")
    @PreAuthorize("@ss.hasPermission('als:deposit-refund-apply:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDepositRefundApplyExcel(@Valid DepositRefundApplyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DepositRefundApplyDO> list = depositRefundApplyService.getDepositRefundApplyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "退押申请.xls", "数据", DepositRefundApplyRespVO.class,
                        BeanUtils.toBean(list, DepositRefundApplyRespVO.class));
    }

    @PutMapping("/dealDepositRefund")
    @Operation(summary = "处理退押申请")
    @PreAuthorize("@ss.hasPermission('als:deposit-refund-apply:update')")
    public CommonResult<Boolean> dealDepositRefund(@Valid @RequestBody DepositRefundDealReqVO reqVO) {
        reqVO.setLoginUserId(SecurityFrameworkUtils.getLoginUserId());
        lessonHourFacade.dealDepositRefund(reqVO);
        return success(true);
    }

}
