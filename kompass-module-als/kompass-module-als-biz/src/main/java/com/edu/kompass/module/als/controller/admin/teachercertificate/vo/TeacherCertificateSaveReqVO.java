package com.edu.kompass.module.als.controller.admin.teachercertificate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

@Schema(description = "管理后台 - 老师证书新增/修改 Request VO")
@Data
public class TeacherCertificateSaveReqVO {

    @Schema(description = "证书ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16607")
    private Long certificateId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2144")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "老师姓名不能为空")
    private String teacherName;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "性别不能为空")
    private String teacherSex;

    @Schema(description = "证书照片URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String picUrl;

    @Schema(description = "身份证", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "身份证不能为空")
    private String teacherIdNumber;

    @Schema(description = "证书编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String certificateNo;

    @Schema(description = "证书状态 0有效 1无效-已过期 2无效-已注销", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer certificateStatus;

    @Schema(description = "有效期", requiredMode = Schema.RequiredMode.REQUIRED)
    private Date validTime;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String remark;

}
