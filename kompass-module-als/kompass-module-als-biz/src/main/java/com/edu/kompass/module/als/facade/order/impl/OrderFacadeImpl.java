package com.edu.kompass.module.als.facade.order.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.edu.kompass.framework.common.enums.CommonStatusEnum;
import com.edu.kompass.framework.common.exception.util.ServiceExceptionUtil;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.pojo.map.AddressInfo;
import com.edu.kompass.framework.common.pojo.map.GeoCode;
import com.edu.kompass.framework.common.pojo.map.GeoCodeResponse;
import com.edu.kompass.framework.common.pojo.map.Regeocode;
import com.edu.kompass.framework.common.util.collection.ArrayUtils;
import com.edu.kompass.framework.common.util.map.AddressLocationUtil;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.ip.core.utils.AreaUtils;
import com.edu.kompass.framework.security.core.util.SecurityFrameworkUtils;
import com.edu.kompass.module.als.controller.admin.order.vo.*;
import com.edu.kompass.module.als.controller.admin.teacher.vo.TeacherRespVO;
import com.edu.kompass.module.als.dal.dataobject.customer.CustomerDO;
import com.edu.kompass.module.als.dal.dataobject.order.OrderDO;
import com.edu.kompass.module.als.dal.dataobject.teacher.TeacherDO;
import com.edu.kompass.module.als.dal.dataobject.teacherfav.TeacherFavDO;
import com.edu.kompass.module.als.enums.*;
import com.edu.kompass.module.als.facade.order.OrderFacade;
import com.edu.kompass.module.als.service.customer.CustomerService;
import com.edu.kompass.module.als.service.customerpackage.CustomerPackageService;
import com.edu.kompass.module.als.service.order.OrderService;
import com.edu.kompass.module.als.service.orderconfirm.OrderConfirmService;
import com.edu.kompass.module.als.service.teacher.TeacherService;
import com.edu.kompass.module.als.service.teacherfav.TeacherFavService;
import com.edu.kompass.module.system.dal.dataobject.dict.DictDataDO;
import com.edu.kompass.module.system.dal.dataobject.user.AdminUserDO;
import com.edu.kompass.module.system.service.dict.DictDataService;
import com.edu.kompass.module.system.service.user.AdminUserService;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.edu.kompass.module.als.enums.LogRecordConstants.*;
import static com.edu.kompass.module.system.enums.DictTypeConstants.ALS_SOURCE_CHANNEL;

@Service
@Slf4j
public class OrderFacadeImpl implements OrderFacade {

    @Resource
    private OrderService orderService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    private OrderConfirmService orderConfirmService;
    @Resource
    private CustomerService customerService;
    @Resource
    private TeacherService teacherService;
    @Resource
    private CustomerPackageService customerPackageService;
    @Resource
    private DictDataService dictDataService;
    @Resource
    private TeacherFavService teacherFavService;

    @Override
    public OrderRespVO getOrder(Long orderId) {
        OrderDO order = orderService.getOrder(orderId);
        if (Objects.isNull(order)){
            return null;
        }
        OrderRespVO result = BeanUtils.toBean(order, OrderRespVO.class);

        if (Objects.nonNull(order.getMatchTeacherId())){
            TeacherDO teacher = teacherService.getTeacher(order.getMatchTeacherId());
            result.setMatchTeacher(teacher.getTeacherName());
        }
        result.setCommunicatePre(ArrayUtils.toIntList(order.getCommunicatePre()));
        result.setTimeRange(ArrayUtils.toIntList(order.getTimeRange()));
        result.setNeedsTags(ArrayUtils.toIntList(order.getNeedsTags()));
        result.setHardRequireAbility(ArrayUtils.toIntList(order.getHardRequireAbility()));
        result.setNeedsFocusTags(ArrayUtils.toIntList(order.getNeedsFocusTags()));
        result.setTrackingRemarkTags(ArrayUtils.toIntList(order.getTrackingRemarkTags()));
        return result;
    }

    @Override
    public PageResult<OrderRespVO> getOrderPage(OrderPageReqVO pageReqVO) {
        PageResult<OrderDO> pageResult = orderService.getOrderPage(pageReqVO);

        PageResult<OrderRespVO> result = BeanUtils.toBean(pageResult, OrderRespVO.class);
        List<OrderRespVO> list = result.getList();
        if (CollUtil.isEmpty(list)){
            return PageResult.empty();
        }
        List<Long> customerIds = list.stream().map(OrderRespVO::getCustomerId).distinct().collect(Collectors.toList());
        Map<Long, CustomerDO> customerMap = getCustomerMap(customerIds);

        Set<Long> teacherIds = list.stream()
                .flatMap(order -> {
                    Set<Long> ids = new HashSet<>();
                    ids.add(order.getChangedTeacherId());
                    ids.add(order.getMatchTeacherId());
                    return ids.stream();
                })
                .collect(Collectors.toSet());

        Map<Long, TeacherRespVO> teacherMap = getTeacherMap(new ArrayList<>(teacherIds));

        List<AdminUserDO> allUser = adminUserService.getUserListByStatus(CommonStatusEnum.ENABLE.getStatus());
        Map<Long, String> userMap = allUser.stream().collect(Collectors.toMap(AdminUserDO::getId, AdminUserDO::getNickname));
        if (MapUtil.isEmpty(userMap)){
            userMap = MapUtil.newHashMap();
        }

        List<Long> orderIds = list.stream().map(OrderRespVO::getOrderId).distinct().collect(Collectors.toList());
        // 接单确认数量
        Map<Long,Integer> orderConfirmCountMap = orderConfirmService.getOrderConfirmByOrderIds(orderIds);
        // 收藏记录
        List<TeacherFavDO> teacherFavList = teacherFavService.getTeacherFavList(orderIds);
        // 按订单分组
        Map<Long, List<TeacherFavDO>> favMap = teacherFavList.stream().collect(Collectors.groupingBy(TeacherFavDO::getOrderId));
        for (OrderRespVO orderRespVO : list) {
            String area = AreaUtils.format(orderRespVO.getOrderAreaId());
            orderRespVO.setOrderAreaName(area);
            orderRespVO.setHeadCurrentName(userMap.get(orderRespVO.getHeadCurrent()));
            orderRespVO.setHeadMarketName(userMap.get(orderRespVO.getHeadMarket()));
            orderRespVO.setHeadOperateName(userMap.get(orderRespVO.getHeadOperate()));
            
            orderRespVO.setReleaseUser(userMap.get(orderRespVO.getReleaseUserId()));

            CustomerDO customerDO = customerMap.get(orderRespVO.getCustomerId());
            orderRespVO.setCustomerName(Optional.ofNullable(customerDO).map(CustomerDO::getCustomerName).orElse(""));

            TeacherRespVO changedTeacher = teacherMap.get(orderRespVO.getChangedTeacherId());
            orderRespVO.setChangedTeacher(Objects.isNull(changedTeacher) ? "" : changedTeacher.getTeacherName());
            orderRespVO.setChangedTeacherPhone(Objects.isNull(changedTeacher) ? "" : changedTeacher.getTeacherPhone());

            TeacherRespVO matchTeacher = teacherMap.get(orderRespVO.getMatchTeacherId());
            orderRespVO.setMatchTeacher(Objects.isNull(matchTeacher) ? "" : matchTeacher.getTeacherName());
            orderRespVO.setMatchTeacherPhone(Objects.isNull(matchTeacher) ? "" : matchTeacher.getTeacherPhone());

            // 接单数量
            Integer confirmCount = orderConfirmCountMap.get(orderRespVO.getOrderId());
            orderRespVO.setConfirmCount(Objects.isNull(confirmCount) ? 0 : confirmCount);

            List<TeacherFavDO> respVOS = favMap.get(orderRespVO.getOrderId());
            if (CollUtil.isNotEmpty(respVOS)){
                // 按类型分组
                Map<Integer, List<TeacherFavDO>> collect = respVOS.stream().collect(Collectors.groupingBy(TeacherFavDO::getType));
                List<TeacherFavDO> collectionList = collect.get(FavTypeEnum.COLLECTION.getCode());
                List<TeacherFavDO> viewList = collect.get(FavTypeEnum.VIEWED.getCode());
                orderRespVO.setFavCollectionCount(CollUtil.isNotEmpty(collectionList) ? collectionList.size() : 0);
                orderRespVO.setFavReadCount(CollUtil.isNotEmpty(viewList) ? viewList.size() : 0);
            }
        }
        return result;
    }

    private Map<Long, CustomerDO> getCustomerMap(List<Long> customerIds) {
        List<CustomerDO> customerList = customerService.getCustomerList(customerIds);
        return customerList.stream().collect(Collectors.toMap(CustomerDO::getCustomerId, Function.identity()));
    }

    private Map<Long, TeacherRespVO> getTeacherMap(List<Long> changedTeacherIds) {
        List<TeacherRespVO> teacherList = teacherService.getTeacherList(changedTeacherIds);
        return teacherList.stream().collect(Collectors.toMap(TeacherRespVO::getTeacherId, Function.identity()));
    }

    @Override
    public void addTeacherOrder(OrderAddTeacherReqVo orderTrackDateReqVo) {
        Long orderId = orderTrackDateReqVo.getOrderId();
        Long newOrderId = orderService.copyOrder(orderId);

        OrderUpdateReqVO updateReqVO = new OrderUpdateReqVO();
        updateReqVO.setOrderId(newOrderId);
        updateReqVO.setSourceChannel(OrderSourceChannelEnum.ZJ_LS.getCode());
        updateReqVO.setOrderRemark(orderTrackDateReqVo.getAddReason());
        orderService.updateOrderById(updateReqVO);
    }

    @Override
    @Transactional
    public void changeTeacherOrder(OrderChangeTeacherReqVo orderChangeTeacherReqVo) {
        Long orderId = orderChangeTeacherReqVo.getOrderId();
        Long newOrderId = orderService.copyOrder(orderId);
        log.info("复制订单，新订单ID：{}", newOrderId);

        OrderUpdateReqVO updateReqVO = new OrderUpdateReqVO();
        updateReqVO.setOrderId(newOrderId);
        updateReqVO.setSourceChannel(2); //换老师
        updateReqVO.setChangedTeacherId(orderChangeTeacherReqVo.getChangedTeacherId());
        updateReqVO.setChangedReasonTags(orderChangeTeacherReqVo.getChangedReasonTags());
        updateReqVO.setChangedReason(orderChangeTeacherReqVo.getChangedReason());
        // todo 老师评价
        orderService.updateOrderById(updateReqVO);
        
        // 更新原订单
    }

    @Override
    public void auditOrder(Long orderId) {
        OrderDO order = orderService.getOrder(orderId);
        if (Objects.isNull(order)){
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        List<Integer> preConditionStatus = Arrays.asList(OrderStatusEnum.PENDING_IMPROVEMENT.getCode(), OrderStatusEnum.PENDING_REVIEW.getCode());
        if (!preConditionStatus.contains(order.getOrderStatus())){
            // 订单状态前提是：待完善或待审核，如果不是，直接报错
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_ASYNC_UPDATED,"当前状态："+OrderStatusEnum.fromCode(order.getOrderStatus()));
        }

        OrderSaveReqVO updateReqVO = new OrderSaveReqVO();
        updateReqVO.setOrderId(orderId);
        updateReqVO.setOrderStatus(OrderStatusEnum.PENDING_CONFIRMATION.getCode());
        updateReqVO.setAuditTime(LocalDateTime.now());
        updateReqVO.setAuditUserId(SecurityFrameworkUtils.getLoginUserId());
        log.info("审核订单,入参：{}", JSON.toJSON(updateReqVO));
        orderService.updateOrderWithoutValid(updateReqVO);
    }

    @Override
    public void pauseOrder(Long orderId) {
        log.info("暂停订单,orderId：{}", orderId);
        changeSuspend(orderId,YNEnum.Y);
    }

    @Override
    public void startOrder(Long orderId) {
        log.info("开启订单,orderId：{}", orderId);
        changeSuspend(orderId,YNEnum.N);
    }

    private void changeSuspend(Long orderId,YNEnum isSuspend){
        log.info("开启订单,orderId：{}", orderId);
        OrderDO order = orderService.getOrder(orderId);
        if (Objects.isNull(order)){
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        OrderUpdateReqVO updateReqVO = new OrderUpdateReqVO();
        updateReqVO.setOrderId(orderId);
        updateReqVO.setIsSuspend(isSuspend.getCode());
        orderService.updateOrderById(updateReqVO);
    }



    @Override
    public void releaseOrder(Long orderId) {
        OrderDO order = orderService.getOrder(orderId);
        if (Objects.isNull(order)){
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_NOT_EXISTS);
        }
        if (!Objects.equals(order.getReleaseStatus(),PublishStatusEnum.NOT_STARTED.getCode())){
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.ORDER_ASYNC_UPDATED,"当前状态："+PublishStatusEnum.fromCode(order.getReleaseStatus()));
        }
        OrderSaveReqVO updateReqVO = new OrderSaveReqVO();
        updateReqVO.setOrderId(orderId);
        updateReqVO.setReleaseStatus(PublishStatusEnum.PUBLISHING.getCode());
        updateReqVO.setReleaseTime(LocalDateTime.now());
        updateReqVO.setReleaseUserId(SecurityFrameworkUtils.getLoginUserId());

        updateReqVO.setOrderStatus(OrderStatusEnum.PUBLISHING.getCode());
        log.info("发布订单,入参：{}", JSON.toJSON(updateReqVO));
        orderService.updateOrderWithoutValid(updateReqVO);
    }

    @Override
    public List<OrderMapRespVO> getOrderMapList(OrderPageReqVO pageReqVO) {
        List<OrderDO> orderList = orderService.getOrderList(pageReqVO);
        if (CollUtil.isEmpty(orderList)){
            return null;
        }
        List<Long> customerIds = orderList.stream().map(OrderDO::getCustomerId).collect(Collectors.toList());

        List<OrderMapRespVO> respVOList = BeanUtils.toBean(orderList, OrderMapRespVO.class);
        // 客户信息
        List<CustomerDO> customerList = customerService.getCustomerList(customerIds);
        Map<Long, CustomerDO> customerMap = customerList.stream().collect(Collectors.toMap(CustomerDO::getCustomerId, Function.identity()));

        // 运营信息
        List<AdminUserDO> userList = adminUserService.getUserListByStatus(CommonStatusEnum.ENABLE.getStatus());
        Map<Long, AdminUserDO> userMap = userList.stream().collect(Collectors.toMap(AdminUserDO::getId, Function.identity()));

        // 剩余课时
        List<Long> orderIds = orderList.stream().map(OrderDO::getOrderId).collect(Collectors.toList());
        Map<Long, BigDecimal> customerRemainLessonPeriod = customerPackageService.getCustomerRemainLessonPeriod(customerIds);

        // 接单确认数量
        Map<Long,Integer> orderConfirmCountMap = orderConfirmService.getOrderConfirmByOrderIds(orderIds);

        List<DictDataDO> alsSourceChannel = dictDataService.getDictDataListByDictType(ALS_SOURCE_CHANNEL);
        Map<String, String> sourceChannelMap = alsSourceChannel.stream().collect(Collectors.toMap(DictDataDO::getValue, DictDataDO::getLabel));
        for (OrderMapRespVO orderMapRespVO : respVOList) {
            CustomerDO customerDO = customerMap.get(orderMapRespVO.getCustomerId());
            orderMapRespVO.setCustomerName(customerDO.getCustomerName());
            orderMapRespVO.setCustomerPhone(customerDO.getCustomerPhone());

            AdminUserDO adminUserDO = userMap.get(orderMapRespVO.getHeadOperate());
            orderMapRespVO.setHeadOperateName(Optional.ofNullable(adminUserDO).map(AdminUserDO::getNickname).orElse(""));

            Integer confirmCount = orderConfirmCountMap.get(orderMapRespVO.getOrderId());
            orderMapRespVO.setConfirmCount(Objects.isNull(confirmCount) ? 0 : confirmCount);

            BigDecimal bigDecimal = customerRemainLessonPeriod.get(orderMapRespVO.getCustomerId());
            orderMapRespVO.setLessonPeriodRemain(bigDecimal.toPlainString());

            orderMapRespVO.setRequireSexDesc(AlsSexEnum.fromCode(orderMapRespVO.getRequireSex()));

            String s = sourceChannelMap.get(String.valueOf(orderMapRespVO.getSourceChannel()));
            orderMapRespVO.setSourceChannelDesc(s);
        }
        return respVOList;
    }

    @Override
    public void updateLocal(Long id) {
        OrderDO order = orderService.getOrder(id);
        if (Objects.isNull(order)){
            return;
        }
        String areaName = AreaUtils.getFullName(order.getOrderAreaId());
        String orderAddress = order.getOrderAddress();
        GeoCodeResponse llByAddress = AddressLocationUtil.getLLByAddress(areaName + orderAddress);
        if (Objects.isNull(llByAddress)){
            return;
        }
        List<GeoCode> geocodes = llByAddress.getGeocodes();
        if (CollUtil.isEmpty(geocodes)){
            return;
        }
        GeoCode geoCode = geocodes.get(0);
        if (Objects.isNull(geoCode)){
            return;
        }
        String location = Optional.ofNullable(geoCode.getLocation()).orElse(null);
        if (StrUtil.isBlank(location)){
            return;
        }
        String[] split = location.split(",");
        if (split.length != 2){
            return;
        }
        AddressInfo addressByLL = AddressLocationUtil.getAddressByLL(location);
        if (Objects.isNull(addressByLL)){
            return;
        }
        Regeocode regeocode = addressByLL.getRegeocode();
        OrderSaveReqVO updateReqVO = new OrderSaveReqVO();
        updateReqVO.setOrderId(id);
        updateReqVO.setLongitude(split[0]);
        updateReqVO.setLatitude(split[1]);
        updateReqVO.setParseAddress(Optional.ofNullable(regeocode).map(Regeocode::getFormattedAddress).orElse(""));
        orderService.updateOrderWithoutValid(updateReqVO);
    }

    @Override
    @LogRecord(type = ALS_ORDER_TYPE, subType = ALS_ORDER_SUB_TYPE, bizNo = "{{#updateReqVO.orderId}}",
            success = ALS_ORDER_UPDATE_SUCCESS)
    public void updateOrder(OrderSaveReqVO updateReqVO) {
        OrderDO orderDO = orderService.getOrder(updateReqVO.getOrderId());

        orderService.updateOrder(updateReqVO);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(orderDO, OrderSaveReqVO.class));
        LogRecordContext.putVariable("orderId", updateReqVO.getOrderId());
    }
}
