package com.edu.kompass.module.als.controller.admin.teacherability.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 老师能力分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherAbilityPageReqVO extends PageParam {

    @Schema(description = "外语")
    private String foreignLanguage;

    @Schema(description = "外语证明")
    private String foreignCertificate;

    @Schema(description = "四级分数")
    private Integer gradeFour;

    @Schema(description = "六级分数")
    private Integer gradeSix;

    @Schema(description = "高考英语")
    private Integer englishScore;

    @Schema(description = "雅思")
    private BigDecimal ieltsScore;

    @Schema(description = "托福")
    private BigDecimal toeflScore;

    @Schema(description = "钢琴等级")
    private String pianoLevel;

    @Schema(description = "钢琴证书颁证方")
    private String pianoCertificateIssuer;

    @Schema(description = "其他技能证书及获奖情况")
    private String otherCertificate;

    @Schema(description = "在校获奖情况")
    private String schoolAwards;

    @Schema(description = "补充奖项")
    private String schoolAwardsExtra;

    @Schema(description = "特长")
    private Integer forte;

    @Schema(description = "其他特长")
    private String forteExtra;

    @Schema(description = "家教经历")
    private String experience;

    @Schema(description = "教学方法")
    private String teachingMethod;

    @Schema(description = "作业辅导科目擅长排序")
    private String teachScopeRank;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
