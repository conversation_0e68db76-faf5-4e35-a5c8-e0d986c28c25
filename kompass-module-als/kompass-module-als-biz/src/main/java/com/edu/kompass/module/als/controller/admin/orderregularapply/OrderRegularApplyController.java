package com.edu.kompass.module.als.controller.admin.orderregularapply;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.audit.AuditVo;
import com.edu.kompass.module.als.controller.admin.orderregularapply.vo.OrderRegularApplyPageReqVO;
import com.edu.kompass.module.als.controller.admin.orderregularapply.vo.OrderRegularApplyRespVO;
import com.edu.kompass.module.als.controller.admin.orderregularapply.vo.OrderRegularApplySaveReqVO;
import com.edu.kompass.module.als.controller.admin.orderregularapply.vo.RegularApplyAuditReqVO;
import com.edu.kompass.module.als.dal.dataobject.orderregularapply.OrderRegularApplyDO;
import com.edu.kompass.module.als.facade.order.OrderRegularApplyFacade;
import com.edu.kompass.module.als.service.orderregularapply.OrderRegularApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 正式课申请")
@RestController
@RequestMapping("/als/order-regular-apply")
@Validated
public class OrderRegularApplyController {

    @Resource
    private OrderRegularApplyService orderRegularApplyService;
    @Resource
    private OrderRegularApplyFacade orderRegularApplyFacade;

    @PostMapping("/create")
    @Operation(summary = "创建正式课申请")
    @PreAuthorize("@ss.hasPermission('als:order-regular-apply:create')")
    public CommonResult<Long> createOrderRegularApply(@Valid @RequestBody OrderRegularApplySaveReqVO createReqVO) {
        return success(orderRegularApplyService.createOrderRegularApply(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新正式课申请")
    @PreAuthorize("@ss.hasPermission('als:order-regular-apply:update')")
    public CommonResult<Boolean> updateOrderRegularApply(@Valid @RequestBody OrderRegularApplySaveReqVO updateReqVO) {
        orderRegularApplyService.updateOrderRegularApplyValid(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除正式课申请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:order-regular-apply:delete')")
    public CommonResult<Boolean> deleteOrderRegularApply(@RequestParam("id") Long id) {
        orderRegularApplyService.deleteOrderRegularApply(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得正式课申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:order-regular-apply:query')")
    public CommonResult<OrderRegularApplyRespVO> getOrderRegularApply(@RequestParam("id") Long id) {
        OrderRegularApplyDO orderRegularApply = orderRegularApplyService.getOrderRegularApply(id);
        return success(BeanUtils.toBean(orderRegularApply, OrderRegularApplyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得正式课申请分页")
    @PreAuthorize("@ss.hasPermission('als:order-regular-apply:query')")
    public CommonResult<PageResult<OrderRegularApplyRespVO>> getOrderRegularApplyPage(@Valid OrderRegularApplyPageReqVO pageReqVO) {
        PageResult<OrderRegularApplyDO> pageResult = orderRegularApplyService.getOrderRegularApplyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OrderRegularApplyRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出正式课申请 Excel")
    @PreAuthorize("@ss.hasPermission('als:order-regular-apply:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOrderRegularApplyExcel(@Valid OrderRegularApplyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OrderRegularApplyDO> list = orderRegularApplyService.getOrderRegularApplyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "正式课申请.xls", "数据", OrderRegularApplyRespVO.class,
                        BeanUtils.toBean(list, OrderRegularApplyRespVO.class));
    }

    @PutMapping("/audit/feedback")
    @Operation(summary = "反馈表审核")
    @PreAuthorize("@ss.hasPermission('als:order-regular-apply:update')")
    public CommonResult<Boolean> auditFeedback(@Valid @RequestBody AuditVo auditVo) {
        orderRegularApplyFacade.auditFeedback(auditVo);
        return success(true);
    }

    @PutMapping("/audit/plan")
    @Operation(summary = "陪学计划审核")
    @PreAuthorize("@ss.hasPermission('als:order-regular-apply:update')")
    public CommonResult<Boolean> auditPlan(@Valid @RequestBody AuditVo auditVo) {
        orderRegularApplyFacade.auditPlan(auditVo);
        return success(true);
    }
}
