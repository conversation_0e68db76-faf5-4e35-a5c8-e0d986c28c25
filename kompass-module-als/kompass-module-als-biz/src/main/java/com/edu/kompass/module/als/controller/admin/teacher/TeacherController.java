package com.edu.kompass.module.als.controller.admin.teacher;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.teacher.vo.*;
import com.edu.kompass.module.als.dal.dataobject.teacher.TeacherDO;
import com.edu.kompass.module.als.facade.teacher.TeacherFacade;
import com.edu.kompass.module.als.service.teacher.TeacherService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 老师类")
@RestController
@RequestMapping("/als/teacher")
@Validated
public class TeacherController {

    @Resource
    private TeacherFacade teacherFacade;
    @Resource
    private TeacherService teacherService;

    @PostMapping("/create")
    @Operation(summary = "创建老师类")
    @PreAuthorize("@ss.hasPermission('als:teacher:create')")
    public CommonResult<Long> createTeacher(@Valid @RequestBody TeacherSaveReqVO createReqVO) {
        return success(teacherService.createTeacher(createReqVO));
    }

    @PutMapping("/uploadAttachment")
    @Operation(summary = "上传老师资料附件")
    @PreAuthorize("@ss.hasPermission('als:teacher:update')")
    public CommonResult<Boolean> uploadAttachment(@Valid @RequestBody TeacherAttachmentVO updateReqVO) {
        teacherService.uploadAttachment(updateReqVO);
        return success(true);
    }

    @GetMapping("/queryAttachment")
    @Operation(summary = "查询老师资料附件")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher:query')")
    public CommonResult<TeacherAttachmentVO> queryAttachment(@RequestParam("id") Long id) {
        TeacherAttachmentVO vo = teacherFacade.queryAttachment(id);
        return success(vo);
    }

    @PutMapping("/update")
    @Operation(summary = "更新老师类")
    @PreAuthorize("@ss.hasPermission('als:teacher:update')")
    public CommonResult<Boolean> updateTeacher(@Valid @RequestBody TeacherSaveReqVO updateReqVO) {
        teacherService.updateTeacher(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除老师类")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:teacher:delete')")
    public CommonResult<Boolean> deleteTeacher(@RequestParam("id") Long id) {
        teacherService.deleteTeacher(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得老师类")
    @Parameter(name = "id", description = "老师ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher:query')")
    public CommonResult<TeacherRespVO> getTeacher(@RequestParam("id") Long id) {
        TeacherRespVO teacher = teacherFacade.getTeacher(id);
        return success(teacher);
    }

    @GetMapping("/detail")
    @Operation(summary = "获得老师详情")
    @Parameter(name = "id", description = "老师ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher:query')")
    public CommonResult<TeacherDetailVO> getTeacherDetail(@RequestParam("id") Long id) {
        TeacherDetailVO teacher = teacherFacade.getDetail(id);
        return success(teacher);
    }

    @GetMapping("/bindPage")
    @Operation(summary = "绑定老师分页列表")
    @PreAuthorize("@ss.hasPermission('als:teacher:query')")
    public CommonResult<PageResult<TeacherBindRespVO>> getTeacherBindPage(@Valid TeacherBindPageReqVO pageReqVO) {
        PageResult<TeacherBindRespVO> teacherPage = teacherFacade.getTeacherBindPage(pageReqVO);
        return success(teacherPage);
    }

    @GetMapping("/page")
    @Operation(summary = "老师分页列表")
    @PreAuthorize("@ss.hasPermission('als:teacher:query')")
    public CommonResult<PageResult<TeacherRespVO>> getTeacherPage(@Valid TeacherPageReqVO pageReqVO) {
        PageResult<TeacherRespVO> teacherPage = teacherFacade.getTeacherPage(pageReqVO);
        return success(teacherPage);
    }
    @GetMapping("/queryByKeywords")
    @Operation(summary = "老师分页列表")
    @PreAuthorize("@ss.hasPermission('als:teacher:query')")
    public CommonResult<List<TeacherRespVO>> queryByKeywords(@Valid TeacherPageReqVO pageReqVO) {
        List<TeacherRespVO> teacherList = teacherFacade.queryByKeywords(pageReqVO);
        return success(teacherList);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出老师类 Excel")
    @PreAuthorize("@ss.hasPermission('als:teacher:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTeacherExcel(@Valid TeacherPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TeacherDO> list = teacherService.getTeacherPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "老师类.xls", "数据", TeacherRespVO.class,
                        BeanUtils.toBean(list, TeacherRespVO.class));
    }

    @GetMapping("/getResume")
    @Operation(summary = "获得老师简历")
    @Parameter(name = "id", description = "老师ID", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher:query')")
    public CommonResult<TeacherResumeVO> getResume(@RequestParam("id") Long id) {
        TeacherResumeVO teacher = teacherFacade.getResume(id);
        return success(teacher);
    }

    @GetMapping("/updateLocal")
    @Operation(summary = "更新位置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher:query')")
    public CommonResult<Boolean> updateLocal(@RequestParam("id") Long id) {
        teacherFacade.updateLocal(id);
        return success(true);
    }

}
