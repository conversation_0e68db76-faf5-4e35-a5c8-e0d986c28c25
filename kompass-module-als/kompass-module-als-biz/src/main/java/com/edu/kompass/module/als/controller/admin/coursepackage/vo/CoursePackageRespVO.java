package com.edu.kompass.module.als.controller.admin.coursepackage.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 课时包 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CoursePackageRespVO {

    @Schema(description = "课时包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25891")
    @ExcelProperty("课时包ID")
    private Long coursePackageId;

    @Schema(description = "课时包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("课时包名称")
    private String packageName;

    @Schema(description = "课时包类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "课时包类型", converter = DictConvert.class)
    @DictFormat("als_package_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer packageType;

    @Schema(description = "总课时数", requiredMode = Schema.RequiredMode.REQUIRED, example = "60")
    @ExcelProperty("总课时数")
    private Integer lessonPeriod;

    @Schema(description = "售价", requiredMode = Schema.RequiredMode.REQUIRED, example = "11312")
    @ExcelProperty("售价")
    private BigDecimal salePrice;

    @Schema(description = "是否启用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "是否启用", converter = DictConvert.class)
    @DictFormat("als_yes_or_on") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isEnable;

}
