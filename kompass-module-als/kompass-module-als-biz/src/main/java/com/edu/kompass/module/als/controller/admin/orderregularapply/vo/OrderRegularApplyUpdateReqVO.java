package com.edu.kompass.module.als.controller.admin.orderregularapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 正式课申请修改 Request VO")
@Data
public class OrderRegularApplyUpdateReqVO {

    @Schema(description = "正式课申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5357")
    private Long orderRegularApplyId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "22856")
    private Long orderId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11830")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12397")
    private Long teacherId;

    @Schema(description = "体验课反馈简述", requiredMode = Schema.RequiredMode.REQUIRED)
    private String feedbackDesc;

    @Schema(description = "体验课反馈表", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String feedbackUrl;

    @Schema(description = "正式课陪学计划表", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String planUrl;

    @Schema(description = "陪学计划ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25424")
    private Long planLessonId;

    @Schema(description = "陪学公约", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String agreedUrl;

    @Schema(description = "反馈表审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer feedbackAuditStatus;

    @Schema(description = "反馈表审核时间")
    private LocalDateTime feedbackAuditTime;

    @Schema(description = "反馈表审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "2796")
    private Long feedbackAuditUserId;

    @Schema(description = "反馈表审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    private String feedbackAuditRemark;

    @Schema(description = "来自家长的评价", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer customerEvaluation;

    @Schema(description = "正式课陪学计划审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer planAuditStatus;

    @Schema(description = "正式课陪学计划审核时间")
    private LocalDateTime planAuditTime;

    @Schema(description = "正式课陪学计划审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "3878")
    private Long planAuditUserId;

    @Schema(description = "正式课陪学计划审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String planAuditRemark;

}
