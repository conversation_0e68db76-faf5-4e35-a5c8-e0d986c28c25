package com.edu.kompass.module.als.controller.admin.refundapply.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 家长退款申请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RefundApplyPageReqVO extends PageParam {

    @Schema(description = "退款ID", example = "28461")
    private Long refundApplyId;

    @Schema(description = "购买记录ID", example = "10453")
    private Long customerPackageId;

    @Schema(description = "家长ID", example = "10409")
    private Long customerId;

    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "退款理由选择", example = "1")
    private Integer refundReasonType;

    @Schema(description = "退款理由", example = "不喜欢")
    private String refundReason;

    @Schema(description = "申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] applyTime;

    @Schema(description = "退款状态", example = "2")
    private Integer refundStatus;

    @Schema(description = "处理人", example = "24518")
    private Long dealUserId;

    @Schema(description = "退款种类", example = "2")
    private Integer refundType;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "备注时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] remarkTime;

    @Schema(description = "备注人", example = "26346")
    private Long remarkUserId;

    @Schema(description = "解决方案及复盘")
    private String replay;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
