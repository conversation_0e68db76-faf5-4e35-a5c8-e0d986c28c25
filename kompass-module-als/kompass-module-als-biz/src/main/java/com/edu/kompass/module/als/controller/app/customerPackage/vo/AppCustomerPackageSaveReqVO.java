package com.edu.kompass.module.als.controller.app.customerPackage.vo;

import com.edu.kompass.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 已购课时包记录新增/修改 Request VO")
@Data
public class AppCustomerPackageSaveReqVO extends BaseDO {

    @Schema(description = "购买记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5792")
    private Long customerPackageId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31048")
    private Long customerId;

    @Schema(description = "课时包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String packageName;

    @NotNull(message = "课时包类型不能为空")
    private Integer packageType;

    @Schema(description = "实付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal actualAmount;

    @Schema(description = "优惠金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal discountAmount;

    @Schema(description = "总课时数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lessonPeriod;

    @Schema(description = "已使用课时数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lessonPeriodUsed;

    @Schema(description = "剩余课时", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lessonPeriodRemain;

    @Schema(description = "使用状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer useStatus;

    @Schema(description = "第几次购买", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer buyTimes;

    @Schema(description = "添加课时申请ID", example = "8082")
    private Long customerAddApplyId;

}
