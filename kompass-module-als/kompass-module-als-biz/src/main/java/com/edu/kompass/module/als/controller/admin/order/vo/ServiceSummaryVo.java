package com.edu.kompass.module.als.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Service
@Data
public class ServiceSummaryVo {

    @Schema(description = "家长姓名")
    private String customerName;

    @Schema(description = "服务课时数")
    private BigDecimal serviceClassHour;

    @Schema(description = "上门次数")
    private Integer lessonRecordNum;

    @Schema(description = "好评次数")
    private String goodNum;

    @Schema(description = "中评次数")
    private String mediumNum;

    @Schema(description = "差评次数")
    private String badNum;

    @Schema(description = "请假次数")
    private Integer leaveNum;

    @Schema(description = "请假率")
    private String leaveRadio;

    @Schema(description = "孩子信息")
    private String childInfo;

    @Schema(description = "孩子阶段")
    private Integer kidStage;

    @Schema(description = "学校")
    private String schoolName;

    @Schema(description = "陪学要求")
    private String demandContent;

    @Schema(description = "最后服务时间")
    private LocalDateTime lastServiceTime;
}
