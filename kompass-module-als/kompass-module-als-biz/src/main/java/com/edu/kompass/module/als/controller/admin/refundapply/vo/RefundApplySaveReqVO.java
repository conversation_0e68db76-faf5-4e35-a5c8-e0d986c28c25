package com.edu.kompass.module.als.controller.admin.refundapply.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 家长退款申请新增/修改 Request VO")
@Data
public class RefundApplySaveReqVO {

    @Schema(description = "退款ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28461")
    private Long refundApplyId;

    @Schema(description = "购买记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10453")
    @NotNull(message = "购买记录ID不能为空")
    private Long customerPackageId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10409")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "退款课时数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退款课时数不能为空")
    private BigDecimal refundClassHour;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "退款金额不能为空")
    private BigDecimal refundAmount;

    @Schema(description = "退款理由选择", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "退款理由选择不能为空")
    private Integer refundReasonType;

    @Schema(description = "退款理由", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    @NotEmpty(message = "退款理由不能为空")
    private String refundReason;

    @Schema(description = "退款状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "退款状态不能为空")
    private Integer refundStatus;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED, example = "24518")
    @NotNull(message = "处理人不能为空")
    private Long auditUserId;

    @Schema(description = "退款种类", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "退款种类不能为空")
    private Integer refundType;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "备注不能为空")
    private String remark;

    @Schema(description = "备注时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "备注时间不能为空")
    private LocalDateTime remarkTime;

    @Schema(description = "备注人", requiredMode = Schema.RequiredMode.REQUIRED, example = "26346")
    @NotNull(message = "备注人不能为空")
    private Long remarkUserId;

    @Schema(description = "解决方案及复盘", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "解决方案及复盘不能为空")
    private String replay;

}
