package com.edu.kompass.module.als.controller.admin.bind.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 解除绑定审核 Request VO")
@Data
public class UnbindAuditReqVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    private String auditRemark;

}
