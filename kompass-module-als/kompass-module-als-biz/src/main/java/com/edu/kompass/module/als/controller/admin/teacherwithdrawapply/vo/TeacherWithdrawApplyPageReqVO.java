package com.edu.kompass.module.als.controller.admin.teacherwithdrawapply.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 老师提现申请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherWithdrawApplyPageReqVO extends PageParam {

    @Schema(description = "主键", example = "19074")
    private Long teacherWithdrawApplyId;

    @Schema(description = "老师ID", example = "1407")
    private Long teacherId;

    @Schema(description = "老师姓名", example = "张三")
    private String teacherName;

    @Schema(description = "到账金额")
    private BigDecimal toAccountAmount;

    @Schema(description = "手续费")
    private BigDecimal fee;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] applyTime;

    @Schema(description = "审核状态", example = "1")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "审核人", example = "16527")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "你猜")
    private String auditRemark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
