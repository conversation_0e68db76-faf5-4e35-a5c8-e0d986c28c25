package com.edu.kompass.module.als.controller.app.lessonrecord;


import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.module.als.controller.app.lessonrecord.vo.AppLessonRecordPageReqVO;
import com.edu.kompass.module.als.controller.app.lessonrecord.vo.AppLessonRecordRespVO;
import com.edu.kompass.module.als.controller.app.lessonrecord.vo.AppLessonRecordSaveReqVO;
import com.edu.kompass.module.als.facade.app.teacher.AppLessonRecordFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static com.edu.kompass.framework.common.pojo.CommonResult.success;
import static com.edu.kompass.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

@Tag(name = "app - 陪学记录")
@RestController
@RequestMapping("/als/lesson-record")
@Validated
public class AppLessonRecordController {
    
    @Resource
    private AppLessonRecordFacade appLessonRecordFacade;

    @GetMapping("/page")
    @Operation(summary = "陪学记录分页列表")
    @PermitAll
    public CommonResult<PageResult<AppLessonRecordRespVO>> getLessonRecordPage(@Valid AppLessonRecordPageReqVO pageReqVO) {
        PageResult<AppLessonRecordRespVO> pageResult = appLessonRecordFacade.pageList(pageReqVO);
        return success(pageResult);
    }

//    @GetMapping("/get")
//    @Operation(summary = "陪学记录详情")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PermitAll
//    public CommonResult<AppLessonRecordRespVO> getLessonRecord(@RequestParam("id") Long id) {
//        AppLessonRecordRespVO lessonRecord = appLessonRecordFacade.getLessonRecord(id);
//        return success(lessonRecord);
//    }

    /**
     * 开始记录
     * @param createReqVO
     * @return
     */
    @GetMapping("/create")
    @Operation(summary = "开始记录")
    @PermitAll
    public CommonResult<AppLessonRecordRespVO> createLessonRecord(@Valid AppLessonRecordSaveReqVO createReqVO) {
        AppLessonRecordRespVO lessonRecord = appLessonRecordFacade.createLessonRecord(createReqVO);
        return success(lessonRecord);
    }

    /**
     * 更新记录
     * @param createReqVO
     * @return
     */
    @GetMapping("/update")
    @Operation(summary = "更新记录")
    @PermitAll
    public CommonResult<Boolean> updateLessonRecord(@Valid AppLessonRecordSaveReqVO createReqVO) {
        appLessonRecordFacade.updateLessonRecord(createReqVO);
        return success(true);
    }
}
