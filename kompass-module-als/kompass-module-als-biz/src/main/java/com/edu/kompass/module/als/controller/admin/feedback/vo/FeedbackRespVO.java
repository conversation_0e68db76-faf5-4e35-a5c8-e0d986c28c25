package com.edu.kompass.module.als.controller.admin.feedback.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 问题反馈 Response VO")
@Data
@ExcelIgnoreUnannotated
public class FeedbackRespVO {

    @Schema(description = "反馈ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23964")
    @ExcelProperty("反馈ID")
    private Long feedbackId;

    @Schema(description = "用户类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "用户类型", converter = DictConvert.class)
    @DictFormat("als_user_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer memberType;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8735")
    @ExcelProperty("用户ID")
    private Long memberId;

    @Schema(description = "反馈类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "反馈类型", converter = DictConvert.class)
    @DictFormat("als_feedback_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer feedbackType;

    @Schema(description = "反馈标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("反馈标题")
    private String title;

    @Schema(description = "反馈内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("反馈内容")
    private String content;

    @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("图片地址")
    private String picUrl;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("als_deal_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer feedbackStatus;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}