package com.edu.kompass.module.als.controller.admin.customer.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 家长 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CustomerDetailVO {

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2115")
    @ExcelProperty("家长ID")
    private Long customerId;

    @Schema(description = "家长姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("家长姓名")
    private String customerName;

    @Schema(description = "家长性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "家长性别", converter = DictConvert.class)
    @DictFormat("als_sex") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer customerSex;

    @Schema(description = "剩余课时", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @ExcelProperty("剩余课时")
    private BigDecimal remainPeriod;

    @Schema(description = "孩子关系", requiredMode = Schema.RequiredMode.REQUIRED, example = "爸爸")
    @ExcelProperty("孩子关系")
    private String relationship;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("手机号")
    private String customerPhone;

    @Schema(description = "openId", requiredMode = Schema.RequiredMode.REQUIRED, example = "6313")
    @ExcelProperty("openId")
    private String openId;

    @Schema(description = "服务状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "服务状态", converter = DictConvert.class)
    @DictFormat("als_service_tags") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer serviceStatus;

    @Schema(description = "获客渠道", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "获客渠道", converter = DictConvert.class)
    @DictFormat("als_source_channel") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer sourceChannel;

    @Schema(description = "服务标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "服务标签", converter = DictConvert.class)
    @DictFormat("als_service_tags") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> serviceTags;

    @Schema(description = "运营标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "运营标签", converter = DictConvert.class)
    @DictFormat("als_operation_tags") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> operationTags;

    @Schema(description = "分级标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "分级标签", converter = DictConvert.class)
    @DictFormat("als_level_tags") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> levelTags;

    @Schema(description = "注册时间")
    @ExcelProperty("注册时间")
    private LocalDateTime registerTime;

    @Schema(description = "最近登录时间")
    @ExcelProperty("最近登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "运营负责人", requiredMode = Schema.RequiredMode.REQUIRED, example = "7453")
    private Long headOperateUserId;

    @ExcelProperty("运营负责人")
    private String headOperateUserName;

    @Schema(description = "市场负责人", requiredMode = Schema.RequiredMode.REQUIRED, example = "9044")
    private Long headMarketUserId;

    @ExcelProperty("市场负责人")
    private String headMarketUserName;

    @Schema(description = "家长备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("家长备注")
    private String customerRemark;

    @Schema(description = "创建者", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建者")
    private Integer creator;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "服务过的老师", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> serviceTeacherNameList;

    @Schema(description = "购买次数")
    private Integer buyTimes;

    @Schema(description = "总课时")
    private BigDecimal lessonPeriod;

    @Schema(description = "已使用课时")
    private BigDecimal lessonPeriodUsed;

    @Schema(description = "剩余课时")
    private BigDecimal lessonPeriodRemain;

    @Schema(description = "最近购买时间")
    private LocalDateTime lastBuyTime;

}
