package com.edu.kompass.module.als.controller.admin.withdrawapply.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 提现申请分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class WithdrawApplyPageReqVO extends PageParam {

    @Schema(description = "会员ID", example = "783")
    private Long memberId;

    @Schema(description = "提现金额")
    private BigDecimal amount;

    @Schema(description = "提现手续费")
    private BigDecimal fee;

    @Schema(description = "提现类型", example = "1")
    private Integer type;

    @Schema(description = "账号")
    private String accountNo;

    @Schema(description = "收款码", example = "https://www.iocoder.cn")
    private String accountQrCodeUrl;

    @Schema(description = "申请时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] applyTime;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "审核人", example = "4192")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "你说的对")
    private String auditRemark;

    @Schema(description = "提现状态", example = "0")
    private Integer withdrawStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
