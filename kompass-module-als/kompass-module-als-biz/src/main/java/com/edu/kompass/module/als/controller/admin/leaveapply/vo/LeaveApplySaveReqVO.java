package com.edu.kompass.module.als.controller.admin.leaveapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 请假申请列表新增/修改 Request VO")
@Data
public class LeaveApplySaveReqVO {

    @Schema(description = "请假申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13415")
    private Long leaveApplyId;

    @Schema(description = "课时记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3633")
    @NotNull(message = "课时记录ID不能为空")
    private Long orderRecordId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1648")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12836")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12929")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "陪学阶段", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "陪学阶段不能为空")
    private Integer stage;

    @Schema(description = "请假方", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "请假方不能为空")
    private Integer whoLeave;

    @Schema(description = "原定上课时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "原定上课时间不能为空")
    private LocalDateTime planTime;

    @Schema(description = "调整后上课时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "调整后上课时间不能为空")
    private LocalDateTime newTime;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "申请时间不能为空")
    private LocalDateTime applyTime;

    @Schema(description = "申请备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @NotEmpty(message = "申请备注不能为空")
    private String applyRemark;

    @Schema(description = "审核状态", example = "2")
    private Integer auditStatus;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", example = "15912")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "你说的对")
    private String auditRemark;

}
