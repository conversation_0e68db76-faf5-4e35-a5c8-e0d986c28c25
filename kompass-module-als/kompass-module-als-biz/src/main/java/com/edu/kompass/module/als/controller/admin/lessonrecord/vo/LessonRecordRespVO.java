package com.edu.kompass.module.als.controller.admin.lessonrecord.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 陪学记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LessonRecordRespVO {

    @Schema(description = "陪学记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31357")
    @ExcelProperty("陪学记录ID")
    private Long lessonRecordId;

    @Schema(description = "购买记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18033")
    @ExcelProperty("购买记录ID")
    private Long customerPackageId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25142")
    @ExcelProperty("家长ID")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17468")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "上课类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "上课类型", converter = DictConvert.class)
    @DictFormat("als_lesson_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer lessonType;
    
    @Schema(description = "记录来源", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "记录来源", converter = DictConvert.class)
    @DictFormat("als_record_source")
    private Integer recordSource;

    @Schema(description = "陪学人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "陪学人数", converter = DictConvert.class)
    @DictFormat("als_child_number") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer childNumber;

    @Schema(description = "陪学内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("陪学内容")
    private String lessonContent;

    @Schema(description = "陪学记录状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "陪学记录状态", converter = DictConvert.class)
    @DictFormat("als_record_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer recordStatus;

    @Schema(description = "填写进度", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "填写进度", converter = DictConvert.class)
    @DictFormat("als_process") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer process;

    @Schema(description = "课前准备项打分", requiredMode = Schema.RequiredMode.REQUIRED, example = "[4,5,5]")
    @NotEmpty(message = "课前准备项打分不能为空")
    private List<Integer> prepareScore;

    @Schema(description = "课后项打分", requiredMode = Schema.RequiredMode.REQUIRED, example = "[4,5]")
    @NotEmpty(message = "课后项打分不能为空")
    private List<Integer> summaryScore;

    @Schema(description = "课前准备事项", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "课前准备事项", converter = DictConvert.class)
    @DictFormat("als_prepare_item") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer>  prepareItem;

    @Schema(description = "课后总结事项", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "课后总结事项", converter = DictConvert.class)
    @DictFormat("als_summary_item") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer>  summaryItem;

    @Schema(description = "薄弱点记录", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("薄弱点记录")
    private String weakSpot;

    @Schema(description = "行为表现评价", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("行为表现评价")
    private String showEvaluate;

    @Schema(description = "陪学反思", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("陪学反思")
    private String reflect;

    @Schema(description = "给家长留言", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("给家长留言")
    private String leaveWord;

    @Schema(description = "开始打卡时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开始打卡时间")
    private LocalDateTime startTime;

    @Schema(description = "结束打卡时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("结束打卡时间")
    private LocalDateTime endTime;

    @Schema(description = "陪学时长", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("陪学时长")
    private BigDecimal scheduleHour;

    @Schema(description = "下次上课时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("下次上课时间")
    private LocalDateTime nextTime;

    @Schema(description = "提交时间")
    @ExcelProperty("提交时间")
    private LocalDateTime commitTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
