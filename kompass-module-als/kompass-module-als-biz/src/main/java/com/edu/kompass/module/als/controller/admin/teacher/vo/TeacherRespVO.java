package com.edu.kompass.module.als.controller.admin.teacher.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Schema(description = "管理后台 - 老师类 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TeacherRespVO {

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15326")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "是否关注公众号")
    private Boolean isAttention;
    
    @Schema(description = "已关注平台")
    private List<String> attentionPlat;

    @Schema(description = "个人证件照")
    private String personImgUrl;
    
    @Schema(description = "上岗证编号")
    private String workCertificateNo;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("老师姓名")
    private String teacherName;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "17302589290")
    @ExcelProperty("手机号")
    private String teacherPhone;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "性别", converter = DictConvert.class)
    @DictFormat("als_sex") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer teacherSex;

    @Schema(description = "微信号")
    @ExcelProperty("微信号")
    private String wechat;

    @Schema(description = "QQ")
    @ExcelProperty("QQ")
    private String qq;

    @Schema(description = "身份证号")
    @ExcelProperty("身份证号")
    private String idNumber;

    @Schema(description = "出生日期")
    @ExcelProperty("出生日期")
    private Date birth;

    @Schema(description = "政治面貌", example = "1")
    @ExcelProperty(value = "政治面貌", converter = DictConvert.class)
    @DictFormat("als_political_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer politicalStatus;

    @Schema(description = "籍贯", requiredMode = Schema.RequiredMode.REQUIRED, example = "17640")
    @ExcelProperty("籍贯")
    private Integer nativeAreaId;

    @Schema(description = "籍贯")
    private String nativeAreaName;

    @Schema(description = "接单城市", requiredMode = Schema.RequiredMode.REQUIRED, example = "10656")
    @ExcelProperty("接单城市Id")
    private Integer orderCityId;

    @ExcelProperty("接单城市")
    private String orderCity;

    @Schema(description = "接单区域", requiredMode = Schema.RequiredMode.REQUIRED, example = "27286")
    @ExcelProperty("接单区域")
    private List<Integer> orderAreaId;

    @Schema(description = "接单区域", requiredMode = Schema.RequiredMode.REQUIRED, example = "27286")
    @ExcelProperty("接单区域")
    private String orderArea;

    @Schema(description = "大学名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("大学名称")
    private String universityName;

    @Schema(description = "校区")
    @ExcelProperty("校区")
    private String campus;

    @Schema(description = "高校城市", requiredMode = Schema.RequiredMode.REQUIRED, example = "5626")
    private Integer universityCityId;

    @Schema(description = "高校城市", requiredMode = Schema.RequiredMode.REQUIRED, example = "5626")
    private String universityCity;

    @Schema(description = "在校状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer schoolStatus;

    @Schema(description = "专业", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("专业")
    private String profession;

    @Schema(description = "学历", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer degree;

    @Schema(description = "入学年份", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入学年份")
    private Integer entryYear;

    @Schema(description = "授课范围", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("授课范围")
    private List<Integer> teachScope;

    @Schema(description = "时间范围", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("时间范围")
    private List<Integer> teachTimeRange;

    @Schema(description = "经验值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("经验值")
    private Integer expValue;

    @Schema(description = "信用值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("信用值")
    private Integer creditValue;

    @Schema(description = "开始接单日期")
    @ExcelProperty("开始接单日期")
    private LocalDateTime startOrderTime;

    @Schema(description = "有无经验", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "有无经验", converter = DictConvert.class)
    @DictFormat("als_yes_or_on") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isHaveExperience;

    @Schema(description = "本市现住址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("本市现住址")
    private String address;

    private Integer addressAreaId;

    @Schema(description = "可接受单程车程", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("可接受单程车程")
    private Integer acceptableTime;

    @Schema(description = "注册时间")
    @ExcelProperty("注册时间")
    private LocalDateTime registerTime;

    @Schema(description = "来源渠道 ", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "来源渠道 ", converter = DictConvert.class)
    private Integer teacherChannel;

    @Schema(description = "跟踪时间")
    @ExcelProperty("跟踪时间")
    private LocalDateTime trackingTime;

    @Schema(description = "跟踪备注", example = "你猜")
    @ExcelProperty("跟踪备注")
    private String trackingRemark;

    @Schema(description = "抢单次数")
    @ExcelProperty("抢单次数")
    private Integer orderTimes;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "是否启用")
    @ExcelProperty(value = "是否启用", converter = DictConvert.class)
    @DictFormat("als_yes_or_on") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isEnable;

    @Schema(description = "是否可接单")
    @ExcelProperty(value = "是否可接单", converter = DictConvert.class)
    @DictFormat("als_yes_or_on") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isAcceptOrder;

    @Schema(description = "身份标签")
    @ExcelProperty(value = "身份标签", converter = DictConvert.class)
    @DictFormat("als_id_tags") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private List<Integer> idTags;

    @Schema(description = "openId", example = "20845")
    @ExcelProperty("openId")
    private String openId;

    @Schema(description = "最后服务时间")
    @ExcelProperty("最后服务时间")
    private LocalDateTime lastServiceTime;

    @Schema(description = "最后登录时间")
    @ExcelProperty("最后登录时间")
    private LocalDateTime lastActiveTime;

    @Schema(description = "最近抢单时间")
    @ExcelProperty("最近抢单时间")
    private LocalDateTime acceptOrderTime;

    @Schema(description = "运营备注", example = "你猜")
    @ExcelProperty("运营备注")
    private String operationRemark;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "全部认证资料")
    private List<String> srcList ;

    @Schema(description = "个人照片")
    private String url;

    @Schema(description = "面试官评价")
    private String interviewerEvaluate;

    @Schema(description = "面试结果")
    private Integer interviewerLevel;
    
    @Schema(description = "面试时间")
    private LocalDateTime interviewTime;
    
    @Schema(description = "师资备注")
    private String teacherRemark;
}
