package com.edu.kompass.module.als.controller.admin.lessonrecord;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.lessonrecord.vo.LessonRecordPageReqVO;
import com.edu.kompass.module.als.controller.admin.lessonrecord.vo.LessonRecordRespVO;
import com.edu.kompass.module.als.controller.admin.lessonrecord.vo.LessonRecordSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.lessonrecord.LessonRecordDO;
import com.edu.kompass.module.als.service.lessonrecord.LessonRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 陪学记录")
@RestController
@RequestMapping("/als/lesson-record")
@Validated
public class LessonRecordController {

    @Resource
    private LessonRecordService lessonRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建陪学记录")
    @PreAuthorize("@ss.hasPermission('als:lesson-record:create')")
    public CommonResult<Long> createLessonRecord(@Valid @RequestBody LessonRecordSaveReqVO createReqVO) {
        return success(lessonRecordService.createLessonRecord(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新陪学记录")
    @PreAuthorize("@ss.hasPermission('als:lesson-record:update')")
    public CommonResult<Boolean> updateLessonRecord(@Valid @RequestBody LessonRecordSaveReqVO updateReqVO) {
        lessonRecordService.updateLessonRecord(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除陪学记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:lesson-record:delete')")
    public CommonResult<Boolean> deleteLessonRecord(@RequestParam("id") Long id) {
        lessonRecordService.deleteLessonRecord(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得陪学记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:lesson-record:query')")
    public CommonResult<LessonRecordRespVO> getLessonRecord(@RequestParam("id") Long id) {
        LessonRecordDO lessonRecord = lessonRecordService.getLessonRecord(id);
        return success(BeanUtils.toBean(lessonRecord, LessonRecordRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得陪学记录分页")
    @PreAuthorize("@ss.hasPermission('als:lesson-record:query')")
    public CommonResult<PageResult<LessonRecordRespVO>> getLessonRecordPage(@Valid LessonRecordPageReqVO pageReqVO) {
        PageResult<LessonRecordDO> pageResult = lessonRecordService.getLessonRecordPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LessonRecordRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出陪学记录 Excel")
    @PreAuthorize("@ss.hasPermission('als:lesson-record:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonRecordExcel(@Valid LessonRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonRecordDO> list = lessonRecordService.getLessonRecordPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "陪学记录.xls", "数据", LessonRecordRespVO.class,
                        BeanUtils.toBean(list, LessonRecordRespVO.class));
    }

}
