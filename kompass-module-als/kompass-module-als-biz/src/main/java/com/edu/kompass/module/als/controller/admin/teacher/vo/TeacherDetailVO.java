package com.edu.kompass.module.als.controller.admin.teacher.vo;

import com.edu.kompass.module.als.controller.admin.bind.vo.BindDetailVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 老师类 Response VO")
@Data
public class TeacherDetailVO {

    @Schema(description = "老师详情")
    private TeacherRespVO teacherVo;

    @Schema(description = "服务详情")
    private TeacherServiceVO serviceVo;

    @Schema(description = "已绑定家长")
    private List<BindDetailVO> bindList;
}
