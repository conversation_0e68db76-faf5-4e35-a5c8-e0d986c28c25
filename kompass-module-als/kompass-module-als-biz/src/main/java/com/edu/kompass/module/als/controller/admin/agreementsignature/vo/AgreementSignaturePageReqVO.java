package com.edu.kompass.module.als.controller.admin.agreementsignature.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import com.edu.kompass.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 协议签署记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AgreementSignaturePageReqVO extends PageParam {

    @Schema(description = "协议ID", example = "16707")
    private Long agreementId;

    @Schema(description = "协议唯一标识")
    private String agreementKey;

    @Schema(description = "签署时的协议版本")
    private String agreementVersion;

    @Schema(description = "用户ID", example = "14258")
    private Long userId;

    @Schema(description = "签名图片URL", example = "https://www.iocoder.cn")
    private String signatureUrl;

    @Schema(description = "签署IP地址")
    private String ipAddress;

    @Schema(description = "用户代理信息")
    private String userAgent;

    @Schema(description = "签署时间")
    private LocalDateTime signedAt;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}