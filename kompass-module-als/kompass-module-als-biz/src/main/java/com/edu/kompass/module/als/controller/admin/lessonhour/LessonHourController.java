package com.edu.kompass.module.als.controller.admin.lessonhour;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.audit.AuditVo;
import com.edu.kompass.module.als.controller.admin.lessonhour.vo.LessonHourPageReqVO;
import com.edu.kompass.module.als.controller.admin.lessonhour.vo.LessonHourRespVO;
import com.edu.kompass.module.als.controller.admin.lessonhour.vo.LessonHourSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.lessonhour.LessonHourDO;
import com.edu.kompass.module.als.facade.teacher.LessonHourFacade;
import com.edu.kompass.module.als.service.lessonhour.LessonHourService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课时记录")
@RestController
@RequestMapping("/als/lesson-hour")
@Validated
public class LessonHourController {

    @Resource
    private LessonHourService lessonHourService;

    @Resource
    private LessonHourFacade lessonHourFacade;
    
    @PostMapping("/create")
    @Operation(summary = "创建课时记录")
    @PreAuthorize("@ss.hasPermission('als:lesson-hour:create')")
    public CommonResult<Long> createLessonHour(@Valid @RequestBody LessonHourSaveReqVO createReqVO) {
        return success(lessonHourService.createLessonHour(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课时记录")
    @PreAuthorize("@ss.hasPermission('als:lesson-hour:update')")
    public CommonResult<Boolean> updateLessonHour(@Valid @RequestBody LessonHourSaveReqVO updateReqVO) {
        lessonHourService.updateLessonHourValid(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课时记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:lesson-hour:delete')")
    public CommonResult<Boolean> deleteLessonHour(@RequestParam("id") Long id) {
        lessonHourService.deleteLessonHour(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课时记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:lesson-hour:query')")
    public CommonResult<LessonHourRespVO> getLessonHour(@RequestParam("id") Long id) {
        LessonHourDO lessonHour = lessonHourService.getLessonHour(id);
        return success(BeanUtils.toBean(lessonHour, LessonHourRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "课时记录分页")
    @PreAuthorize("@ss.hasPermission('als:lesson-hour:query')")
    public CommonResult<PageResult<LessonHourRespVO>> getLessonHourPage(@Valid LessonHourPageReqVO pageReqVO) {
        PageResult<LessonHourRespVO> pageResult = lessonHourFacade.getLessonHourPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课时记录 Excel")
    @PreAuthorize("@ss.hasPermission('als:lesson-hour:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonHourExcel(@Valid LessonHourPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonHourDO> list = lessonHourService.getLessonHourPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课时记录.xls", "数据", LessonHourRespVO.class,
                        BeanUtils.toBean(list, LessonHourRespVO.class));
    }

    @PutMapping("/auditLessonHour")
    @Operation(summary = "审核")
    @PreAuthorize("@ss.hasPermission('als:lesson-hour:update')")
    public CommonResult<Boolean> auditLessonHour(@Valid @RequestBody AuditVo auditVo) {
        lessonHourFacade.audit(auditVo);
        return success(true);
    }

}
