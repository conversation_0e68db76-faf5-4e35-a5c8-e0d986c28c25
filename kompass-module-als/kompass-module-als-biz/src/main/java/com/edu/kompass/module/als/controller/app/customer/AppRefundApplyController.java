package com.edu.kompass.module.als.controller.app.customer;

import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.idempotent.core.annotation.Idempotent;
import com.edu.kompass.framework.web.core.util.WebFrameworkUtils;
import com.edu.kompass.module.als.controller.app.customer.vo.AppRefundApplyRespVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppRefundApplySaveReqVO;
import com.edu.kompass.module.als.facade.customer.RefundFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "app - 家长退款申请")
@RestController
@RequestMapping("/als/customer/refund")
@Validated
public class AppRefundApplyController {

    @Resource
    private RefundFacade refundFacade;

    @GetMapping("/calRefundInfo")
    @Operation(summary = "计算退款课时")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<AppRefundApplyRespVO> calRefundInfo(@RequestParam("customerPackageId") Long customerPackageId) {
        AppRefundApplyRespVO respVO = refundFacade.calRefundInfo(customerPackageId);
        return success(respVO);
    }

    @PostMapping("/submit")
    @Operation(summary = "创建家长退款申请")
    @Idempotent(timeout = 10, message = "提交申请中，请勿重复提交")
    public CommonResult<Long> createRefundApply(@Valid @RequestBody AppRefundApplySaveReqVO createReqVO) {
        Long customerMemberId = WebFrameworkUtils.getLoginUserId();
        createReqVO.setCustomerMemberId(customerMemberId);
        return success(refundFacade.createRefundApply(createReqVO));
    }

    @GetMapping("/cancel")
    @Operation(summary = "取消申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @Idempotent(timeout = 10, message = "取消申请中，请勿重复提交")
    public CommonResult<Boolean> cancelRefundApply(@RequestParam("id") Long id) {
        refundFacade.cancelRefundApply(id);
        return success(true);
    }
}
