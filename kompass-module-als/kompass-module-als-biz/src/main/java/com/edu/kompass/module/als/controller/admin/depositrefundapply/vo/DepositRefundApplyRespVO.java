package com.edu.kompass.module.als.controller.admin.depositrefundapply.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 退押申请 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DepositRefundApplyRespVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4377")
    @ExcelProperty("申请ID")
    private Long depositRefundApplyId;

    @Schema(description = "申请人", requiredMode = Schema.RequiredMode.REQUIRED, example = "13924")
    @ExcelProperty("申请人")
    private Long teacherId;

    @Schema(description = "课时记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23575")
    @ExcelProperty("课时记录ID")
    private Long lessonHourId;

    @Schema(description = "申请时间")
    @ExcelProperty("申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "申请备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "不对")
    @ExcelProperty("申请备注")
    private String applyReason;

    @Schema(description = "处理状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "处理状态", converter = DictConvert.class)
    @DictFormat("als_deposit_deal_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer dealStatus;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请时间")
    private LocalDateTime createTime;

}
