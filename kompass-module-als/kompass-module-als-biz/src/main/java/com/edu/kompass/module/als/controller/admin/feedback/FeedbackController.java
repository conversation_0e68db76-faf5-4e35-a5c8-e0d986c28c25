package com.edu.kompass.module.als.controller.admin.feedback;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

import com.edu.kompass.framework.excel.core.util.ExcelUtils;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.*;

import com.edu.kompass.module.als.controller.admin.feedback.vo.*;
import com.edu.kompass.module.als.dal.dataobject.feedback.FeedbackDO;
import com.edu.kompass.module.als.service.feedback.FeedbackService;

@Tag(name = "管理后台 - 问题反馈")
@RestController
@RequestMapping("/als/feedback")
@Validated
public class FeedbackController {

    @Resource
    private FeedbackService feedbackService;

    @PostMapping("/create")
    @Operation(summary = "创建问题反馈")
    @PreAuthorize("@ss.hasPermission('als:feedback:create')")
    public CommonResult<Long> createFeedback(@Valid @RequestBody FeedbackSaveReqVO createReqVO) {
        return success(feedbackService.createFeedback(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新问题反馈")
    @PreAuthorize("@ss.hasPermission('als:feedback:update')")
    public CommonResult<Boolean> updateFeedback(@Valid @RequestBody FeedbackSaveReqVO updateReqVO) {
        feedbackService.updateFeedback(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除问题反馈")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:feedback:delete')")
    public CommonResult<Boolean> deleteFeedback(@RequestParam("id") Long id) {
        feedbackService.deleteFeedback(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得问题反馈")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:feedback:query')")
    public CommonResult<FeedbackRespVO> getFeedback(@RequestParam("id") Long id) {
        FeedbackDO feedback = feedbackService.getFeedback(id);
        return success(BeanUtils.toBean(feedback, FeedbackRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得问题反馈分页")
    @PreAuthorize("@ss.hasPermission('als:feedback:query')")
    public CommonResult<PageResult<FeedbackRespVO>> getFeedbackPage(@Valid FeedbackPageReqVO pageReqVO) {
        PageResult<FeedbackDO> pageResult = feedbackService.getFeedbackPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, FeedbackRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出问题反馈 Excel")
    @PreAuthorize("@ss.hasPermission('als:feedback:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportFeedbackExcel(@Valid FeedbackPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<FeedbackDO> list = feedbackService.getFeedbackPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "问题反馈.xls", "数据", FeedbackRespVO.class,
                        BeanUtils.toBean(list, FeedbackRespVO.class));
    }

}