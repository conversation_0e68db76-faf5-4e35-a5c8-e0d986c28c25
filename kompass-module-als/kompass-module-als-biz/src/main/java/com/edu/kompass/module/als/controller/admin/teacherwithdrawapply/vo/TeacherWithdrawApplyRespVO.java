package com.edu.kompass.module.als.controller.admin.teacherwithdrawapply.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 老师提现申请 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TeacherWithdrawApplyRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19074")
    @ExcelProperty("主键")
    private Long teacherWithdrawApplyId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1407")
    @ExcelProperty("老师ID")
    private Long teacherId;

    @Schema(description = "老师姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("老师姓名")
    private String teacherName;

    @Schema(description = "到账金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("到账金额")
    private BigDecimal toAccountAmount;

    @Schema(description = "手续费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("手续费")
    private BigDecimal fee;

    @Schema(description = "总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总金额")
    private BigDecimal totalAmount;

    @Schema(description = "申请时间")
    @ExcelProperty("申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "等待天数")
    private String waitDays;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat("als_audit_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer auditStatus;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", example = "16527")
    @ExcelProperty("审核人")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "你猜")
    @ExcelProperty("审核备注")
    private String auditRemark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "上次等待天数", example = "1")
    private BigDecimal lastWaitDays;

}
