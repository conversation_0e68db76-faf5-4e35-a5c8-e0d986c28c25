package com.edu.kompass.module.als.controller.admin.customer.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.edu.kompass.module.als.controller.admin.bind.vo.BindDetailVO;
import com.edu.kompass.module.als.controller.admin.order.vo.OrderRespVO;
import com.edu.kompass.module.als.controller.admin.refundapply.vo.RefundApplyRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 家长 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CustomerDetailCollectVO {

    /**
     * 基本信息
     */
   private CustomerDetailVO customerRespVO;

    /**
     * 订单统计信息
     */
    private CustomerOrderDetailVO customerOrderDetailVO;

    /**
     * 最新订单
     */
   private OrderRespVO lastOrder;

    /**
     * 体验单
     */
   private List<OrderRespVO> orderRespVOList;

    /**
     * 绑定信息
     */
   private List<BindDetailVO> bindRespVOList;

    /**
     * 退款信息
     */
   private List<RefundApplyRespVO> refundApplyRespVOList;
}
