package com.edu.kompass.module.als.controller.admin.deposit.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课时押金新增/修改 Request VO")
@Data
public class DepositSaveReqVO {

    @Schema(description = "押金id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3339")
    private Long depositId;

    @Schema(description = "课时记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "22924")
    @NotNull(message = "课时记录ID不能为空")
    private Long lessonHourId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11583")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20596")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "扣押课时", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "扣押课时不能为空")
    private BigDecimal classHour;

    @Schema(description = "押金状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer depositStatus;

    @Schema(description = "押金处理方式", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer dealMethod;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED, example = "26744")
    private Long dealUserId;

    @Schema(description = "处理时间")
    private LocalDateTime dealTime;

    @Schema(description = "家长获得课时-上课时长(h)", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal customerGet;

    @Schema(description = "老师获得课时-上课时长(h)", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal teacherGet;

    @Schema(description = "平台获得课时-上课时长(h)", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal platformGet;

    @Schema(description = "备注时间")
    private LocalDateTime remarkTime;

    @Schema(description = "备注人", requiredMode = Schema.RequiredMode.REQUIRED, example = "13245")
    private Long remarkUserId;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    private String remark;

}
