package com.edu.kompass.module.als.controller.admin.withdrawapply.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 提现申请 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WithdrawApplyRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31414")
    @ExcelProperty("主键")
    private Long withdrawApplyId;

    @Schema(description = "会员ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "783")
    @ExcelProperty("会员ID")
    private Long memberId;

    @Schema(description = "提现金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("提现金额")
    private BigDecimal amount;

    @Schema(description = "提现手续费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("提现手续费")
    private BigDecimal fee;

    @Schema(description = "提现类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("提现类型")
    private Integer type;

    @Schema(description = "账号")
    @ExcelProperty("账号")
    private String accountNo;

    @Schema(description = "收款码", example = "https://www.iocoder.cn")
    @ExcelProperty("收款码")
    private String accountQrCodeUrl;

    @Schema(description = "申请时间")
    @ExcelProperty("申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat("als_audit_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer auditStatus;

    @Schema(description = "审核时间")
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "4192")
    @ExcelProperty("审核人")
    private Long auditUserId;

    @Schema(description = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("审核备注")
    private String auditRemark;

    @Schema(description = "提现状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @ExcelProperty(value = "提现状态", converter = DictConvert.class)
    @DictFormat("als_withdraw_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer withdrawStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
