package com.edu.kompass.module.als.service.async;

import com.edu.kompass.module.als.enums.FavTypeEnum;

/**
 * 异步用户行为服务接口
 * 
 * <AUTHOR>
 */
public interface AsyncUserBehaviorService {
    
    /**
     * 异步记录用户浏览行为
     * 
     * @param orderId 订单ID
     * @param teacherMemberId 老师会员ID
     */
    void recordViewBehaviorAsync(Long orderId, Long teacherMemberId);
    
    /**
     * 异步记录用户收藏行为
     * 
     * @param orderId 订单ID
     * @param teacherMemberId 老师会员ID
     * @param favType 收藏类型
     */
    void recordFavBehaviorAsync(Long orderId, Long teacherMemberId, FavTypeEnum favType);
    
    /**
     * 异步更新订单统计信息
     * 
     * @param orderId 订单ID
     * @param favType 行为类型
     */
    void updateOrderStatisticsAsync(Long orderId, FavTypeEnum favType);
}
