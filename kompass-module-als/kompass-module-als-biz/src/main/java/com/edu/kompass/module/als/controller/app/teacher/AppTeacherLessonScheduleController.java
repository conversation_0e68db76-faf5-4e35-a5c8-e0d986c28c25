package com.edu.kompass.module.als.controller.app.teacher;

import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.module.als.controller.app.teacher.vo.AppLessonScheduleRespVO;
import com.edu.kompass.module.als.controller.app.teacher.vo.AppLessonScheduleSaveReqVO;
import com.edu.kompass.module.als.facade.app.teacher.AppLessonScheduleFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "app - 时间规划执行")
@RestController
@RequestMapping("/als/teacher/schedule")
@Validated
public class AppTeacherLessonScheduleController {

    @Resource
    private AppLessonScheduleFacade appLessonScheduleFacade;

    @PostMapping("/save")
    @Operation(summary = "记录课时")
    public CommonResult<Long> saveLessonPlan(@Valid @RequestBody AppLessonScheduleSaveReqVO saveReqVO) {
        if (saveReqVO.getLessonScheduleId() == null){
            return success(appLessonScheduleFacade.save(saveReqVO));
        }
        return success(saveReqVO.getLessonScheduleId());
    }

    @GetMapping("/list")
    @Operation(summary = "时间计划列表")
    public CommonResult<List<AppLessonScheduleRespVO>> getOrderPage( @RequestParam("lessonRecordId") Long lessonRecordId ) {
        List<AppLessonScheduleRespVO> result = appLessonScheduleFacade.getByLessonRecordId(lessonRecordId);
        return success(result);
    }

    @GetMapping("/end")
    @Operation(summary = "结束")
    public CommonResult<Boolean> end( @RequestParam("lessonScheduleId") Long lessonScheduleId ) {
        appLessonScheduleFacade.end(lessonScheduleId);
        return success(Boolean.TRUE);
    }

    @GetMapping("/delete")
    @Operation(summary = "删除")
    public CommonResult<Boolean> delete( @RequestParam("lessonScheduleId") Long lessonScheduleId ) {
        appLessonScheduleFacade.delete(lessonScheduleId);
        return success(Boolean.TRUE);
    }
    
}
