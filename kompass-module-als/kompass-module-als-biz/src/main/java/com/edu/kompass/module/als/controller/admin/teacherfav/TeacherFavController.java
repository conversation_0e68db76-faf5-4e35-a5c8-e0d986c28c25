package com.edu.kompass.module.als.controller.admin.teacherfav;

import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

import com.edu.kompass.framework.excel.core.util.ExcelUtils;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.*;

import com.edu.kompass.module.als.controller.admin.teacherfav.vo.*;
import com.edu.kompass.module.als.dal.dataobject.teacherfav.TeacherFavDO;
import com.edu.kompass.module.als.service.teacherfav.TeacherFavService;

@Tag(name = "管理后台 - 老师收藏")
@RestController
@RequestMapping("/als/teacher-fav")
@Validated
public class TeacherFavController {

    @Resource
    private TeacherFavService teacherFavService;

    @PostMapping("/create")
    @Operation(summary = "创建老师收藏")
    @PreAuthorize("@ss.hasPermission('als:teacher-fav:create')")
    public CommonResult<Long> createTeacherFav(@Valid @RequestBody TeacherFavSaveReqVO createReqVO) {
        return success(teacherFavService.createTeacherFav(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新老师收藏")
    @PreAuthorize("@ss.hasPermission('als:teacher-fav:update')")
    public CommonResult<Boolean> updateTeacherFav(@Valid @RequestBody TeacherFavSaveReqVO updateReqVO) {
        teacherFavService.updateTeacherFav(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除老师收藏")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:teacher-fav:delete')")
    public CommonResult<Boolean> deleteTeacherFav(@RequestParam("id") Long id) {
        teacherFavService.deleteTeacherFav(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得老师收藏")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher-fav:query')")
    public CommonResult<TeacherFavRespVO> getTeacherFav(@RequestParam("id") Long id) {
        TeacherFavDO teacherFav = teacherFavService.getTeacherFav(id);
        return success(BeanUtils.toBean(teacherFav, TeacherFavRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得老师收藏分页")
    @PreAuthorize("@ss.hasPermission('als:teacher-fav:query')")
    public CommonResult<PageResult<TeacherFavRespVO>> getTeacherFavPage(@Valid TeacherFavPageReqVO pageReqVO) {
        PageResult<TeacherFavDO> pageResult = teacherFavService.getTeacherFavPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TeacherFavRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出老师收藏 Excel")
    @PreAuthorize("@ss.hasPermission('als:teacher-fav:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTeacherFavExcel(@Valid TeacherFavPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TeacherFavDO> list = teacherFavService.getTeacherFavPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "老师收藏.xls", "数据", TeacherFavRespVO.class,
                        BeanUtils.toBean(list, TeacherFavRespVO.class));
    }

}