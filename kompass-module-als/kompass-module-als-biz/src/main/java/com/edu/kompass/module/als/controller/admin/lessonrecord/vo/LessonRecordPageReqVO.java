package com.edu.kompass.module.als.controller.admin.lessonrecord.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 陪学记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LessonRecordPageReqVO extends PageParam {

    @Schema(description = "购买记录ID", example = "18033")
    private Long customerPackageId;

    @Schema(description = "家长ID", example = "25142")
    private Long customerId;

    @Schema(description = "老师ID", example = "17468")
    private Long teacherId;

    @Schema(description = "上课类型", example = "1")
    private Integer lessonType;
    
    @Schema(description = "记录来源", example = "1")
    private Integer recordSource;

    @Schema(description = "陪学人数")
    private Integer childNumber;

    @Schema(description = "陪学内容")
    private String lessonContent;

    @Schema(description = "陪学记录状态", example = "2")
    private Integer recordStatus;

    @Schema(description = "填写进度")
    private Integer process;

    @Schema(description = "课前项打分", example = "[4,5,5]")
    private String prepareScore;

    @Schema(description = "课后项打分", example = "[4,5]")
    private String summaryScore;

    @Schema(description = "课前准备事项")
    private String prepareItem;

    @Schema(description = "课后总结事项")
    private String summaryItem;

    @Schema(description = "薄弱点记录")
    private String weakSpot;

    @Schema(description = "行为表现评价")
    private String showEvaluate;

    @Schema(description = "陪学反思")
    private String reflect;

    @Schema(description = "给家长留言")
    private String leaveWord;

    @Schema(description = "开始打卡时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] startTime;

    @Schema(description = "结束打卡时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] endTime;

    @Schema(description = "陪学时长")
    private BigDecimal scheduleHour;

    @Schema(description = "下次上课时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] nextTime;

    @Schema(description = "提交时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] commitTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
