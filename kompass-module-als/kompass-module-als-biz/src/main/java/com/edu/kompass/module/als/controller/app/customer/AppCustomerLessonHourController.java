package com.edu.kompass.module.als.controller.app.customer;

import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.web.core.util.WebFrameworkUtils;
import com.edu.kompass.module.als.controller.app.lessonhour.vo.AppLessonHourPageReqVO;
import com.edu.kompass.module.als.controller.app.lessonhour.vo.AppLessonHourRespVO;
import com.edu.kompass.module.als.controller.app.lessonrecord.vo.AppLessonRecordRespVO;
import com.edu.kompass.module.als.facade.app.teacher.AppLessonHourFacade;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "app - 课时记录")
@RestController
@RequestMapping("/als/customer/hour")
@Validated
public class AppCustomerLessonHourController {

    @Resource
    private AppLessonHourFacade appLessonHourFacade;

    @GetMapping("/page")
    @Operation(summary = "课时记录分页列表")
    public CommonResult<PageResult<AppLessonHourRespVO>> getLessonHourPage(@Valid AppLessonHourPageReqVO pageReqVO) {
        pageReqVO.setCustomerMemberId(WebFrameworkUtils.getLoginUserId());
        PageResult<AppLessonHourRespVO> pageResult = appLessonHourFacade.getLessonHourPageCustomer(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/get")
    @Operation(summary = "课时详情")
    public CommonResult<AppLessonRecordRespVO> getRecordDetail(Long lessonHourId) {
        Long customerMemberId = WebFrameworkUtils.getLoginUserId();
        AppLessonRecordRespVO lessonRecord = appLessonHourFacade.getRecordByLessonHourId(lessonHourId, customerMemberId);
        return success(lessonRecord);
    }
}
