package com.edu.kompass.module.als.service.async.impl;

import com.edu.kompass.module.als.controller.admin.teacherfav.vo.TeacherFavSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.teacherfav.TeacherFavDO;
import com.edu.kompass.module.als.enums.FavTypeEnum;
import com.edu.kompass.module.als.service.async.AsyncUserBehaviorService;
import com.edu.kompass.module.als.service.teacher.TeacherService;
import com.edu.kompass.module.als.service.teacherfav.TeacherFavService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 异步用户行为服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class AsyncUserBehaviorServiceImpl implements AsyncUserBehaviorService {
    
    @Autowired
    private TeacherService teacherService;
    
    @Autowired
    private TeacherFavService teacherFavService;
    
    @Override
    @Async
    public void recordViewBehaviorAsync(Long orderId, Long teacherMemberId, FavTypeEnum favType) {
        try {
            log.info("异步记录老师行为，orderId：{}, teacherMemberId：{}, favType：{}",
                    orderId, teacherMemberId, favType.getDescription());

            Long teacherId = teacherService.getTeacherIdByMemberId(teacherMemberId);
            if (teacherId == null) {
                log.warn("未找到老师信息，teacherMemberId：{}", teacherMemberId);
                return;
            }

            // 检查是否已经记录过该类型的行为
            TeacherFavDO existingBehavior = teacherFavService.getTeacherIsFav(orderId, teacherId, favType);
            if (Objects.isNull(existingBehavior)) {
                // 记录行为
                TeacherFavSaveReqVO behaviorReqVO = new TeacherFavSaveReqVO();
                behaviorReqVO.setOrderId(orderId);
                behaviorReqVO.setTeacherId(teacherId);
                behaviorReqVO.setType(favType.getCode());
                behaviorReqVO.setCreator(String.valueOf(teacherMemberId));
                behaviorReqVO.setUpdater(String.valueOf(teacherMemberId));
                behaviorReqVO.setCreateTime(LocalDateTime.now());
                behaviorReqVO.setUpdateTime(LocalDateTime.now());

                teacherFavService.createTeacherFav(behaviorReqVO);
                log.info("成功记录老师行为，orderId：{}, teacherId：{}, favType：{}",
                        orderId, teacherId, favType.getDescription());
            } else {
                log.debug("老师已有该行为记录，orderId：{}, teacherId：{}, favType：{}",
                        orderId, teacherId, favType.getDescription());
            }

        } catch (Exception e) {
            log.error("异步记录老师行为失败，orderId：{}, teacherMemberId：{}, favType：{}",
                    orderId, teacherMemberId, favType.getDescription(), e);
        }
    }
}
