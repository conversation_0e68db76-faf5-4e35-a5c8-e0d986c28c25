package com.edu.kompass.module.als.service.async.impl;

import com.edu.kompass.module.als.controller.admin.teacherfav.vo.TeacherFavSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.teacherfav.TeacherFavDO;
import com.edu.kompass.module.als.enums.FavTypeEnum;
import com.edu.kompass.module.als.service.async.AsyncUserBehaviorService;
import com.edu.kompass.module.als.service.teacher.TeacherService;
import com.edu.kompass.module.als.service.teacherfav.TeacherFavService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 异步用户行为服务实现类
 * 
 * <AUTHOR>
 */
@Service
@Slf4j
public class AsyncUserBehaviorServiceImpl implements AsyncUserBehaviorService {
    
    @Autowired
    private TeacherService teacherService;
    
    @Autowired
    private TeacherFavService teacherFavService;
    
    @Override
    @Async("userBehaviorExecutor")
    public void recordViewBehaviorAsync(Long orderId, Long teacherMemberId) {
        try {
            log.info("异步记录老师浏览行为，orderId：{}, teacherMemberId：{}", orderId, teacherMemberId);
            
            Long teacherId = teacherService.getTeacherIdByMemberId(teacherMemberId);
            if (teacherId == null) {
                log.warn("未找到老师信息，teacherMemberId：{}", teacherMemberId);
                return;
            }
            
            // 检查是否已经记录过浏览行为
            TeacherFavDO existingView = teacherFavService.getTeacherIsFav(orderId, teacherId, FavTypeEnum.VIEWED);
            if (Objects.isNull(existingView)) {
                // 记录浏览行为
                TeacherFavSaveReqVO viewReqVO = new TeacherFavSaveReqVO();
                viewReqVO.setOrderId(orderId);
                viewReqVO.setTeacherId(teacherId);
                viewReqVO.setType(FavTypeEnum.VIEWED.getCode());
                
                teacherFavService.createTeacherFav(viewReqVO);
                log.info("成功记录老师浏览行为，orderId：{}, teacherId：{}", orderId, teacherId);
            } else {
                log.debug("老师已浏览过该订单，orderId：{}, teacherId：{}", orderId, teacherId);
            }
            
        } catch (Exception e) {
            log.error("异步记录老师浏览行为失败，orderId：{}, teacherMemberId：{}", orderId, teacherMemberId, e);
        }
    }
    
    @Override
    @Async("userBehaviorExecutor")
    public void recordFavBehaviorAsync(Long orderId, Long teacherMemberId, FavTypeEnum favType) {
        try {
            log.info("异步记录老师收藏行为，orderId：{}, teacherMemberId：{}, favType：{}", 
                    orderId, teacherMemberId, favType.getDescription());
            
            Long teacherId = teacherService.getTeacherIdByMemberId(teacherMemberId);
            if (teacherId == null) {
                log.warn("未找到老师信息，teacherMemberId：{}", teacherMemberId);
                return;
            }
            
            // 检查是否已经记录过该类型的行为
            TeacherFavDO existingFav = teacherFavService.getTeacherIsFav(orderId, teacherId, favType);
            if (Objects.isNull(existingFav)) {
                // 记录收藏行为
                TeacherFavSaveReqVO favReqVO = new TeacherFavSaveReqVO();
                favReqVO.setOrderId(orderId);
                favReqVO.setTeacherId(teacherId);
                favReqVO.setType(favType.getCode());
                
                teacherFavService.createTeacherFav(favReqVO);
                log.info("成功记录老师收藏行为，orderId：{}, teacherId：{}, favType：{}", 
                        orderId, teacherId, favType.getDescription());
            } else {
                log.debug("老师已有该收藏记录，orderId：{}, teacherId：{}, favType：{}", 
                        orderId, teacherId, favType.getDescription());
            }
            
        } catch (Exception e) {
            log.error("异步记录老师收藏行为失败，orderId：{}, teacherMemberId：{}, favType：{}", 
                    orderId, teacherMemberId, favType.getDescription(), e);
        }
    }
    
    @Override
    @Async("commonAsyncExecutor")
    public void updateOrderStatisticsAsync(Long orderId, FavTypeEnum favType) {
        try {
            log.info("异步更新订单统计信息，orderId：{}, favType：{}", orderId, favType.getDescription());
            
            // TODO: 这里可以添加订单统计信息的更新逻辑
            // 比如：更新订单的浏览次数、收藏次数等
            // 可以考虑使用Redis来缓存统计数据，定期同步到数据库
            
            log.info("订单统计信息更新完成，orderId：{}, favType：{}", orderId, favType.getDescription());
            
        } catch (Exception e) {
            log.error("异步更新订单统计信息失败，orderId：{}, favType：{}", orderId, favType.getDescription(), e);
        }
    }
}
