package com.edu.kompass.module.als.controller.admin.order.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 陪学订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderPageReqVO extends PageParam {

    @Schema(description = "主键", example = "1395")
    private Long orderId;

    @Schema(description = "订单编号", example = "PX20240903")
    private String orderNo;

    @Schema(description = "订单类型", example = "0")
    private Integer orderType;

    @Schema(description = "课程类型", example = "0")
    private Integer lessonType;

    @Schema(description = "订单渠道", example = "0")
    private Integer sourceChannel;

    @Schema(description = "家长ID", example = "27437")
    private Long customerId;

    @Schema(description = "手机号", example = "17302589292")
    private String customerPhone;

    @Schema(description = "区域", example = "784")
    private String orderAreaId;

    @Schema(description = "详细地址", example = "江苏省南京市XX小区")
    private String orderAddress;

    @Schema(description = "订单状态", example = "1")
    private Integer orderStatus;

    @Schema(description = "发布状态", example = "1")
    private Integer releaseStatus;

    @Schema(description = "发布人", example = "22980")
    private Long releaseUserId;

    @Schema(description = "发布时间", example = "2024-07-30 22:52:06")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] releaseTime;

    @Schema(description = "进程", example = "1")
    private Integer orderProcess;

    @Schema(description = "是否暂停接单", example = "0")
    private Integer isSuspend;

    @Schema(description = "是否已建群", example = "0")
    private Integer isGroupChat;

    @Schema(description = "当前负责人", example = "1")
    private Integer headCurrent;

    @Schema(description = "运营负责人", example = "1")
    private Integer headOperate;

    @Schema(description = "市场负责人", example = "1")
    private Integer headMarket;

    @Schema(description = "老师性别要求", example = "1")
    private Integer requireSex;

    @Schema(description = "老师能力硬性要求", example = "1")
    private String hardRequireAbility;

    @Schema(description = "老师能力要求补充", example = "要求教育专业")
    private String requireAbilityExtra;

    @Schema(description = "需求标签", example = "1,2")
    private String needsTags;

    @Schema(description = "需求侧重点", example = "1,2")
    private String needsFocusTags;

    @Schema(description = "陪学要求", example = "【到家陪学】")
    private String demandContent;

    @Schema(description = "周次", example = "1")
    private Integer timesWeek;

    @Schema(description = "是否是周末订单", example = "1")
    private Integer isOnWeekend;

    @Schema(description = "陪学时间范围", example = "1,2")
    private String timeRange;

    @Schema(description = "跟踪时间", example = "2024-07-30 22:55:45")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] trackingTime;

    @Schema(description = "实际跟踪时间", example = "2024-07-30 22:55:49")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] actualTrackingTime;

    @Schema(description = "跟踪备注标签", example = "[1,2]")
    private String trackingRemarkTags;

    @Schema(description = "体验时间", example = "2024-07-30 22:55:59")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] expTime;

    @Schema(description = "是否确认体验时间", example = "1")
    private Integer isConfirmExpTime;

    @Schema(description = "孩子性别", example = "1")
    private Integer kidSex;

    @Schema(description = "孩子年级阶段", example = "12")
    private Integer kidStage;

    @Schema(description = "沟通结果", example = "1")
    private Integer communicateResult;

    @Schema(description = "学校名称", example = "张三")
    private String schoolName;

    @Schema(description = "学校性质", example = "1")
    private Integer schoolNature;

    @Schema(description = "孩子性格", example = "1")
    private String kidChr;

    @Schema(description = "邀请人", example = "56")
    private Integer inviterId;

    @Schema(description = "抢单老师", example = "23378")
    private Integer matchTeacherId;

    @Schema(description = "承诺最后服务时间", example = "2024-07-30 22:59:07")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] promisedLastServiceTime;

    @Schema(description = "被换老师ID", example = "18313")
    private Long changedTeacherId;

    @Schema(description = "被换原因标签", example = "1")
    private Integer changedReasonTags;

    @Schema(description = "是否手工创建", example = "1")
    private Integer isManualCrate;

    @Schema(description = "注册下单时间", example = "2024-07-30 22:59:23")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] registerTime;

    @Schema(description = "活动标签", example = "1,2")
    private String activeTags;
    
}
