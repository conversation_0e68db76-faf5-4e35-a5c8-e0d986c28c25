package com.edu.kompass.module.als.controller.admin.evaluation.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 评价分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvaluationPageReqVO extends PageParam {

    @Schema(description = "家长ID", example = "31540")
    private Long customerId;

    @Schema(description = "老师ID", example = "19050")
    private Integer teacherId;

    @Schema(description = "评分")
    private Integer score;

    @Schema(description = "评分等级")
    private String level;

    @Schema(description = "模块", example = "1")
    private Integer module;

    @Schema(description = "评价内容")
    private String content;

    @Schema(description = "处理状态", example = "1")
    private Integer dealStatus;

    @Schema(description = "处理人", example = "9910")
    private Long dealUserId;

    @Schema(description = "跟踪备注", example = "你说的对")
    private String remark;

    @Schema(description = "备注时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] remarkTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
