package com.edu.kompass.module.als.controller.admin.coursepackage;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.coursepackage.vo.CoursePackagePageReqVO;
import com.edu.kompass.module.als.controller.admin.coursepackage.vo.CoursePackageRespVO;
import com.edu.kompass.module.als.controller.admin.coursepackage.vo.CoursePackageSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.coursepackage.CoursePackageDO;
import com.edu.kompass.module.als.service.coursepackage.CoursePackageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课时包")
@RestController
@RequestMapping("/als/course-package")
@Validated
public class CoursePackageController {

    @Resource
    private CoursePackageService coursePackageService;

    @PostMapping("/create")
    @Operation(summary = "创建课时包")
    @PreAuthorize("@ss.hasPermission('als:course-package:create')")
    public CommonResult<Long> createCoursePackage(@Valid @RequestBody CoursePackageSaveReqVO createReqVO) {
        return success(coursePackageService.createCoursePackage(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课时包")
    @PreAuthorize("@ss.hasPermission('als:course-package:update')")
    public CommonResult<Boolean> updateCoursePackage(@Valid @RequestBody CoursePackageSaveReqVO updateReqVO) {
        coursePackageService.updateCoursePackage(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课时包")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:course-package:delete')")
    public CommonResult<Boolean> deleteCoursePackage(@RequestParam("id") Long id) {
        coursePackageService.deleteCoursePackage(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课时包")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:course-package:query')")
    public CommonResult<CoursePackageRespVO> getCoursePackage(@RequestParam("id") Long id) {
        CoursePackageDO coursePackage = coursePackageService.getCoursePackage(id);
        return success(BeanUtils.toBean(coursePackage, CoursePackageRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得课时包分页")
    @PreAuthorize("@ss.hasPermission('als:course-package:query')")
    public CommonResult<PageResult<CoursePackageRespVO>> getCoursePackagePage(@Valid CoursePackagePageReqVO pageReqVO) {
        PageResult<CoursePackageDO> pageResult = coursePackageService.getCoursePackagePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CoursePackageRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课时包 Excel")
    @PreAuthorize("@ss.hasPermission('als:course-package:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCoursePackageExcel(@Valid CoursePackagePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CoursePackageDO> list = coursePackageService.getCoursePackagePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课时包.xls", "数据", CoursePackageRespVO.class,
                        BeanUtils.toBean(list, CoursePackageRespVO.class));
    }

    @GetMapping("/all")
    @Operation(summary = "获得全量课时包")
    @PreAuthorize("@ss.hasPermission('als:course-package:query')")
    public CommonResult<List<CoursePackageRespVO>> getAllCoursePackage() {
        List<CoursePackageDO> coursePackage = coursePackageService.getAllCoursePackage();
        List<CoursePackageRespVO> result = BeanUtils.toBean(coursePackage, CoursePackageRespVO.class);
        return success(result);
    }

}
