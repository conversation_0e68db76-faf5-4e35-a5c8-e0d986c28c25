package com.edu.kompass.module.als.service.teacherfav;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.edu.kompass.module.als.controller.admin.teacherfav.vo.TeacherFavPageReqVO;
import com.edu.kompass.module.als.controller.admin.teacherfav.vo.TeacherFavSaveReqVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppOrderPageReqVO;
import com.edu.kompass.module.als.dal.dataobject.order.OrderExtDo;
import com.edu.kompass.module.als.dal.dataobject.teacherfav.TeacherFavDO;
import com.edu.kompass.module.als.dal.mysql.teacherfav.TeacherFavMapper;
import com.edu.kompass.module.als.enums.FavTypeEnum;
import com.edu.kompass.module.als.enums.OrderReleaseStatusEnum;
import com.edu.kompass.module.als.enums.OrderStatusEnum;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Arrays;

import static com.edu.kompass.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.edu.kompass.module.als.enums.ErrorCodeConstants.TEACHER_FAV_NOT_EXISTS;

/**
 * 老师收藏 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TeacherFavServiceImpl implements TeacherFavService {

    @Resource
    private TeacherFavMapper teacherFavMapper;

    @Override
    public Long createTeacherFav(TeacherFavSaveReqVO createReqVO) {
        // 插入
        TeacherFavDO teacherFav = BeanUtils.toBean(createReqVO, TeacherFavDO.class);
        teacherFavMapper.insert(teacherFav);
        // 返回
        return teacherFav.getTeacherFavId();
    }

    @Override
    public void updateTeacherFav(TeacherFavSaveReqVO updateReqVO) {
        // 更新
        TeacherFavDO updateObj = BeanUtils.toBean(updateReqVO, TeacherFavDO.class);
        teacherFavMapper.updateById(updateObj);
    }

    @Override
    public void deleteTeacherFav(Long id) {
        // 校验存在
        validateTeacherFavExists(id);
        // 删除
        teacherFavMapper.deleteById(id);
    }

    private void validateTeacherFavExists(Long id) {
        if (teacherFavMapper.selectById(id) == null) {
            throw exception(TEACHER_FAV_NOT_EXISTS);
        }
    }

    @Override
    public TeacherFavDO getTeacherFav(Long id) {
        return teacherFavMapper.selectById(id);
    }

    @Override
    public PageResult<TeacherFavDO> getTeacherFavPage(TeacherFavPageReqVO pageReqVO) {
        return teacherFavMapper.selectPage(pageReqVO);
    }

    /**
     * 判断老师是否收藏了订单
     * @param orderId
     * @param teacherId
     * @return
     */
    @Override
    public TeacherFavDO getTeacherIsFav(Long orderId, Long teacherId, FavTypeEnum favTypeEnum) {
        LambdaQueryWrapperX<TeacherFavDO> queryWrapperX = new LambdaQueryWrapperX<>();
        queryWrapperX.eq(TeacherFavDO::getOrderId, orderId)
                .eq(TeacherFavDO::getTeacherId, teacherId)
                .eq(TeacherFavDO::getType, favTypeEnum.getCode())
                .eq(TeacherFavDO::getDeleted, Boolean.FALSE);
        TeacherFavDO teacherFavDO = teacherFavMapper.selectOne(queryWrapperX);
        return teacherFavDO;
    }

    /**
     * 我的收藏
     * @param reqVO
     * @return
     */
    @Override
    public PageResult<OrderExtDo> getOrderCenterFavPage(AppOrderPageReqVO reqVO) {
        // 主动收藏
        reqVO.setFavType(FavTypeEnum.COLLECTION.getCode());
        // 订单状态：3发布中 4已匹配
        reqVO.setOrderStatusList(Arrays.asList(OrderStatusEnum.PUBLISHING.getCode(), OrderStatusEnum.MATCHED.getCode()));
        // 订单发布状态：1发布中 2已被抢
        reqVO.setReleaseStatusList(Arrays.asList(OrderReleaseStatusEnum.PUBLISHING.getCode(),OrderReleaseStatusEnum.GRABBED.getCode()));
        IPage<OrderExtDo> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        
        teacherFavMapper.getOrderCenterFavPage(page,reqVO);
        return new PageResult<>(page.getRecords(), page.getTotal());
    }
}
