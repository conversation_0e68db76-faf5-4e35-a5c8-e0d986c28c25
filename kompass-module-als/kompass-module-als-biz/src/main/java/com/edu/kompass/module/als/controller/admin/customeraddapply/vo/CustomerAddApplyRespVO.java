package com.edu.kompass.module.als.controller.admin.customeraddapply.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课时添加申请 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CustomerAddApplyRespVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9875")
    @ExcelProperty("申请ID")
    private Long customerAddApplyId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "8215")
    @ExcelProperty("家长ID")
    private Long customerId;

    @Schema(description = "家长姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("家长姓名")
    private String customerName;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13012345678")
    @ExcelProperty("手机号")
    private String customerPhone;

    @Schema(description = "课时包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "课时包名称")
    @ExcelProperty("课时包名称")
    private String packageName;

    @Schema(description = "课时包类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "课时包类型", converter = DictConvert.class)
    private Integer packageType;

    @Schema(description = "总课时数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("总课时数")
    private Integer lessonPeriod;

    @Schema(description = "销售价", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @ExcelProperty("销售价")
    private BigDecimal salePrice;

    @Schema(description = "优惠金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @ExcelProperty("优惠金额")
    private BigDecimal discountAmount;

    @Schema(description = "实付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @ExcelProperty("实付金额")
    private BigDecimal actualAmount;

    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "支付方式", converter = DictConvert.class)
    @DictFormat("als_payment_method") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer applyMethod;

    @Schema(description = "封存流转金额", example = "100")
    @ExcelProperty("封存流转金额")
    private BigDecimal blockAmount;

    @Schema(description = "资金流转来源", example = "1")
    @ExcelProperty(value = "资金流转来源", converter = DictConvert.class)
    @DictFormat("als_direction_from") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer directionFrom;

    @Schema(description = "申请理由", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    @ExcelProperty("申请理由")
    private String applyReason;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请时间")
    private LocalDateTime createTime;

    @Schema(description = "申请人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "7357")
    @ExcelProperty("申请人ID")
    private Long creator;

    @Schema(description = "申请人", requiredMode = Schema.RequiredMode.REQUIRED, example = "7357")
    @ExcelProperty("申请人")
    private String creatorName;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "审核状态", converter = DictConvert.class)
    @DictFormat("als_audit_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer auditStatus;

    @Schema(description = "审核时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "31513")
    @ExcelProperty("审核人")
    private Long auditUserId;

    @Schema(description = "审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "31513")
    private String auditUserName;

    @Schema(description = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @ExcelProperty("审核备注")
    private String auditRemark;

}
