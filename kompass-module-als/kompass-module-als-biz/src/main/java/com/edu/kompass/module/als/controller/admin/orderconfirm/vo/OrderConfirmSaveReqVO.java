package com.edu.kompass.module.als.controller.admin.orderconfirm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 接单确认新增/修改 Request VO")
@Data
public class OrderConfirmSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "31244")
    private Long orderConfirmId;

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4414")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25149")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11844")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "处理状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "处理状态不能为空")
    private Integer dealStatus;

    @Schema(description = "拒绝原因ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotNull(message = "拒绝原因不能为空")
    private Integer rejectReasonId;

    @Schema(description = "自定义拒绝接单原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
//    @NotEmpty(message = "自定义原因不能为空")
    private String customReason;

    @Schema(description = "处理时间")
    private LocalDateTime dealTime;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED, example = "31127")
//    @NotNull(message = "处理人不能为空")
    private Long dealUserId;

}
