package com.edu.kompass.module.als.controller.admin.agreementsignature.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 协议签署记录新增/修改 Request VO")
@Data
public class AgreementSignatureSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21071")
    private Long agreementSignatureId;

    @Schema(description = "协议ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16707")
    @NotNull(message = "协议ID不能为空")
    private Long agreementId;

    @Schema(description = "协议唯一标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "协议唯一标识不能为空")
    private String agreementKey;

    @Schema(description = "签署时的协议版本", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "签署时的协议版本不能为空")
    private String agreementVersion;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "14258")
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    private Long memberId;

    @Schema(description = "签名图片URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "签名图片URL不能为空")
    private String signatureUrl;

    @Schema(description = "签署IP地址")
    private String ipAddress;

    @Schema(description = "用户代理信息")
    private String userAgent;

    @Schema(description = "签署时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "签署时间不能为空")
    private Date signedAt;
    
     @Schema(description = "签署地址")
     private String signedAddress;
     
     @Schema(description = "有效期")
     private String periodValidity;

}
