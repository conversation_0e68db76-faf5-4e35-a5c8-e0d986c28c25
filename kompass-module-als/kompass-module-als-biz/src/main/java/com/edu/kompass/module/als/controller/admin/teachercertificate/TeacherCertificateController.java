package com.edu.kompass.module.als.controller.admin.teachercertificate;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.teachercertificate.vo.TeacherCertificatePageReqVO;
import com.edu.kompass.module.als.controller.admin.teachercertificate.vo.TeacherCertificateRespVO;
import com.edu.kompass.module.als.controller.admin.teachercertificate.vo.TeacherCertificateSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.teachercertificate.TeacherCertificateDO;
import com.edu.kompass.module.als.facade.teacher.CertificateFacade;
import com.edu.kompass.module.als.service.teachercertificate.TeacherCertificateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 老师证书")
@RestController
@RequestMapping("/als/teacher-certificate")
@Validated
public class TeacherCertificateController {

    @Resource
    private TeacherCertificateService teacherCertificateService;
    @Resource
    private CertificateFacade certificateFacade;
    

    @PostMapping("/create")
    @Operation(summary = "创建老师证书")
    @PreAuthorize("@ss.hasPermission('als:teacher-certificate:create')")
    public CommonResult<Long> createTeacherCertificate(@Valid @RequestBody TeacherCertificateSaveReqVO createReqVO) {
        return success(teacherCertificateService.createTeacherCertificate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新老师证书")
    @PreAuthorize("@ss.hasPermission('als:teacher-certificate:update')")
    public CommonResult<Boolean> updateTeacherCertificate(@Valid @RequestBody TeacherCertificateSaveReqVO updateReqVO) {
        teacherCertificateService.updateTeacherCertificate(updateReqVO);
        return success(true);
    }

    @PostMapping("/createOrUpdate")
    @Operation(summary = "创建/更新老师证书")
    @PreAuthorize("@ss.hasPermission('als:teacher-certificate:create')")
    public CommonResult<Long> createOrUpdate(@Valid @RequestBody TeacherCertificateSaveReqVO createReqVO) {
        certificateFacade.createOrUpdateTeacherCertificate(createReqVO);
        return success(createReqVO.getCertificateId());
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除老师证书")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:teacher-certificate:delete')")
    public CommonResult<Boolean> deleteTeacherCertificate(@RequestParam("id") Long id) {
        teacherCertificateService.deleteTeacherCertificate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得老师证书")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher-certificate:query')")
    public CommonResult<TeacherCertificateRespVO> getTeacherCertificate(@RequestParam("id") Long id) {
        TeacherCertificateDO teacherCertificate = teacherCertificateService.getTeacherCertificate(id);
        return success(BeanUtils.toBean(teacherCertificate, TeacherCertificateRespVO.class));
    }

    @GetMapping("/getByTeacherId")
    @Operation(summary = "获得老师证书")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher-certificate:query')")
    public CommonResult<TeacherCertificateRespVO> getTeacherCertificateByTeacherId(@RequestParam("teacherId") Long teacherId) {
        TeacherCertificateRespVO respVO = certificateFacade.getTeacherCertificateByTeacherId(teacherId);
        return success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得老师证书分页")
    @PreAuthorize("@ss.hasPermission('als:teacher-certificate:query')")
    public CommonResult<PageResult<TeacherCertificateRespVO>> getTeacherCertificatePage(@Valid TeacherCertificatePageReqVO pageReqVO) {
        PageResult<TeacherCertificateDO> pageResult = teacherCertificateService.getTeacherCertificatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TeacherCertificateRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出老师证书 Excel")
    @PreAuthorize("@ss.hasPermission('als:teacher-certificate:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTeacherCertificateExcel(@Valid TeacherCertificatePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TeacherCertificateDO> list = teacherCertificateService.getTeacherCertificatePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "老师证书.xls", "数据", TeacherCertificateRespVO.class,
                        BeanUtils.toBean(list, TeacherCertificateRespVO.class));
    }

}
