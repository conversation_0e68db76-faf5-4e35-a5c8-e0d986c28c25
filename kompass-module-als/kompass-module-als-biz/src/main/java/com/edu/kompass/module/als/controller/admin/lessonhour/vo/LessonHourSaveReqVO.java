package com.edu.kompass.module.als.controller.admin.lessonhour.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 课时记录新增/修改 Request VO")
@Data
public class LessonHourSaveReqVO {

    @Schema(description = "课时记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10208")
    private Long lessonHourId;

    @Schema(description = "陪学记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17973")
    @NotNull(message = "陪学记录ID不能为空")
    private Long lessonRecordId;

    @Schema(description = "购买记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6746")
    @NotNull(message = "购买记录ID不能为空")
    private Long customerPackageId;

    @Schema(description = "上课类型：1课后陪读 2半天伴读 3全天伴读 4钢琴", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "上课类型：1课后陪读 2半天伴读 3全天伴读 4钢琴不能为空")
    private Integer lessonType;

    @Schema(description = "人数：1一对一 2一对多", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "人数：1一对一 2一对多不能为空")
    private Integer childNumber;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26023")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18478")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "上课时长", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "上课时长不能为空")
    private BigDecimal timeHour;

    @Schema(description = "倍数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "倍数不能为空")
    private BigDecimal multiple;

    @Schema(description = "课时数（时长/标准时长）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "课时数（时长/标准时长）不能为空")
    private BigDecimal lessonHour;

    @Schema(description = "总课时(课时数*倍数）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总课时(课时数*倍数）不能为空")
    private BigDecimal classHour;

    @Schema(description = "老师-课时单价（基础单价*课时标准）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2987")
    @NotNull(message = "老师-课时单价（基础单价*课时标准）不能为空")
    private BigDecimal teacherPrice;

    @Schema(description = "老师-小时单价(课时单价/课时标准)", requiredMode = Schema.RequiredMode.REQUIRED, example = "1451")
    @NotNull(message = "老师-小时单价(课时单价/课时标准)不能为空")
    private BigDecimal teacherHourPrice;

    @Schema(description = "老师-附加总费用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "老师-附加总费用不能为空")
    private BigDecimal teacherExtraCharge;

    @Schema(description = "老师-总薪资", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "老师-总薪资不能为空")
    private BigDecimal teacherAmount;

    @Schema(description = "家长-课时单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "14629")
    @NotNull(message = "家长-课时单价不能为空")
    private BigDecimal customerPrice;

    @Schema(description = "家长-消费金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "家长-消费金额不能为空")
    private BigDecimal customerCost;

    @Schema(description = "下次上课时间 yyyy-mm-dd hh:mm")
    private LocalDateTime nextTime;

    @Schema(description = "记录状态 0未确认 1已确认 2已取消 3处理押金", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "记录状态 0未确认 1已确认 2已取消 3处理押金不能为空")
    private Integer recordStatus;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "审核状态 1审核中 2审核通过 3驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "审核状态 1审核中 2审核通过 3驳回不能为空")
    private Integer auditStatus;

    @Schema(description = "审核人", requiredMode = Schema.RequiredMode.REQUIRED, example = "23337")
    @NotNull(message = "审核人不能为空")
    private Long auditUserId;

    @Schema(description = "审核备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    private String auditRemark;

    @Schema(description = "备注时间")
    private LocalDateTime remarkTime;

    @Schema(description = "备注人", requiredMode = Schema.RequiredMode.REQUIRED, example = "3286")
    @NotNull(message = "备注人不能为空")
    private Long remarkUserId;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String remark;

}
