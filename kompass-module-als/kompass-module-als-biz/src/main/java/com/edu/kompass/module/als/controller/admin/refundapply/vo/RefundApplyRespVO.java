package com.edu.kompass.module.als.controller.admin.refundapply.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.excel.core.annotations.DictFormat;
import com.edu.kompass.framework.excel.core.convert.DictConvert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 家长退款申请 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RefundApplyRespVO {

    @Schema(description = "退款ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "28461")
    @ExcelProperty("退款ID")
    private Long refundApplyId;

    @Schema(description = "购买记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10453")
    @ExcelProperty("购买记录ID")
    private Long customerPackageId;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10409")
    @ExcelProperty("家长ID")
    private Long customerId;

    @Schema(description = "退款课时数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("退款课时数")
    private BigDecimal refundClassHour;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "退款理由选择", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "退款理由选择", converter = DictConvert.class)
    @DictFormat("als_refund_reason_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer refundReasonType;

    @Schema(description = "退款理由", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    @ExcelProperty("退款理由")
    private String refundReason;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请时间")
    private LocalDateTime applyTime;

    @Schema(description = "退款状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "退款状态", converter = DictConvert.class)
    @DictFormat("als_refund_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer refundStatus;

    @Schema(description = "处理人", requiredMode = Schema.RequiredMode.REQUIRED, example = "24518")
    @ExcelProperty("处理人")
    private Long auditUserId;

    private String auditRemark;

    private LocalDateTime auditTime;

    @Schema(description = "退款种类", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "退款种类", converter = DictConvert.class)
    @DictFormat("als_refund_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer refundType;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "备注时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("备注时间")
    private LocalDateTime remarkTime;

    @Schema(description = "备注人", requiredMode = Schema.RequiredMode.REQUIRED, example = "26346")
    @ExcelProperty("备注人")
    private Long remarkUserId;

    @Schema(description = "解决方案及复盘", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("解决方案及复盘")
    private String replay;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    private String waitDays;

}
