package com.edu.kompass.module.als.controller.admin.teacheraccountchange.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 老师账户变更记录新增/修改 Request VO")
@Data
public class TeacherAccountChangeSaveReqVO {

    @Schema(description = "变更ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18214")
    private Long teacherAccountChangeId;

    @Schema(description = "老师ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13993")
    @NotNull(message = "老师ID不能为空")
    private Long teacherId;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "金额不能为空")
    private BigDecimal amount;

    @Schema(description = "变更后余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "变更后余额不能为空")
    private BigDecimal balance;

    @Schema(description = "变更备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @NotEmpty(message = "变更备注不能为空")
    private String remark;

    @Schema(description = "变更业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "变更业务类型不能为空")
    private Integer businessType;

    @Schema(description = "业务ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "6262")
    @NotNull(message = "业务ID不能为空")
    private Long businessId;

}
