package com.edu.kompass.module.als.controller.admin.customeraddapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 课时添加审核 Request VO")
@Data
public class CustomerAuditReqVO {

    @Schema(description = "申请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "9875")
    private Long customerAddApplyId;

    @Schema(description = "审核状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "8215")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "申请理由", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    private String auditRemark;

}
