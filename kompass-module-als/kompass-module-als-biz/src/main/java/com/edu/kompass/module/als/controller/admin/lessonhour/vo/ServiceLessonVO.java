package com.edu.kompass.module.als.controller.admin.lessonhour.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ServiceLessonVO {

    private Long teacherId;

    private Long customerId;

    @Schema(description = "服务次数")
    private Integer serviceTimes;

    @Schema(description = "服务课时总数")
    private BigDecimal serviceClassHour;

}
