package com.edu.kompass.module.als.controller.admin.attachment;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.attachment.vo.AttachmentPageReqVO;
import com.edu.kompass.module.als.controller.admin.attachment.vo.AttachmentRespVO;
import com.edu.kompass.module.als.controller.admin.attachment.vo.AttachmentSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.attachment.AttachmentDO;
import com.edu.kompass.module.als.service.attachment.AttachmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 附件")
@RestController
@RequestMapping("/als/attachment")
@Validated
public class AttachmentController {

    @Resource
    private AttachmentService attachmentService;

    @PostMapping("/create")
    @Operation(summary = "创建附件")
    @PreAuthorize("@ss.hasPermission('als:attachment:create')")
    public CommonResult<Long> createAttachment(@Valid @RequestBody AttachmentSaveReqVO createReqVO) {
        return success(attachmentService.createAttachment(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新附件")
    @PreAuthorize("@ss.hasPermission('als:attachment:update')")
    public CommonResult<Boolean> updateAttachment(@Valid @RequestBody AttachmentSaveReqVO updateReqVO) {
        attachmentService.updateAttachment(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除附件")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:attachment:delete')")
    public CommonResult<Boolean> deleteAttachment(@RequestParam("id") Long id) {
        attachmentService.deleteAttachment(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得附件")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:attachment:query')")
    public CommonResult<AttachmentRespVO> getAttachment(@RequestParam("id") Long id) {
        AttachmentDO attachment = attachmentService.getAttachment(id);
        return success(BeanUtils.toBean(attachment, AttachmentRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得附件分页")
    @PreAuthorize("@ss.hasPermission('als:attachment:query')")
    public CommonResult<PageResult<AttachmentRespVO>> getAttachmentPage(@Valid AttachmentPageReqVO pageReqVO) {
        PageResult<AttachmentDO> pageResult = attachmentService.getAttachmentPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, AttachmentRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出附件 Excel")
    @PreAuthorize("@ss.hasPermission('als:attachment:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportAttachmentExcel(@Valid AttachmentPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<AttachmentDO> list = attachmentService.getAttachmentPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "附件.xls", "数据", AttachmentRespVO.class,
                        BeanUtils.toBean(list, AttachmentRespVO.class));
    }
}
