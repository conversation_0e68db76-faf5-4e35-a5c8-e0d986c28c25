package com.edu.kompass.module.als.controller.admin.teacheraccount.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 老师账户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherAccountPageReqVO extends PageParam {

    @Schema(description = "账户ID", example = "19549")
    private Long teacherAccountId;

    @Schema(description = "老师ID", example = "30538")
    private Long teacherId;

    @Schema(description = "余额")
    private BigDecimal balance;

    @Schema(description = "是否可提现")
    private Integer isCashOut;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
