package com.edu.kompass.module.als.controller.admin.invite.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 邀请新增/修改 Request VO")
@Data
public class InviteSaveReqVO {

    @Schema(description = "邀请ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18062")
    private Long inviteId;

    @Schema(description = "用户ID", example = "20971")
    private Long memberId;

    @Schema(description = "邀请人ID", example = "19271")
    private Long inviteMemberId;

    @Schema(description = "邀请人类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "邀请人类型不能为空")
    private Integer inviteMemberType;

    @Schema(description = "邀请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "邀请时间不能为空")
    private LocalDateTime inviteTime;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer awardStatus;

    @Schema(description = "现金", example = "8228")
    private BigDecimal price;

    @Schema(description = "钱包流水ID", example = "22598")
    private Long walletTransactionId;

    @Schema(description = "课时数")
    private BigDecimal lessonPeriod;

    @Schema(description = "课时包ID", example = "27649")
    private Long customerPackageId;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @NotEmpty(message = "备注不能为空")
    private String remark;

}
