package com.edu.kompass.module.als.controller.admin.orderchangeteacher;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.audit.AuditVo;
import com.edu.kompass.module.als.controller.admin.orderchangeteacher.vo.OrderChangeTeacherPageReqVO;
import com.edu.kompass.module.als.controller.admin.orderchangeteacher.vo.OrderChangeTeacherRespVO;
import com.edu.kompass.module.als.controller.admin.orderchangeteacher.vo.OrderChangeTeacherSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.orderchangeteacher.OrderChangeTeacherDO;
import com.edu.kompass.module.als.facade.customer.ChangeTeacherFacade;
import com.edu.kompass.module.als.service.orderchangeteacher.OrderChangeTeacherService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 更换老师")
@RestController
@RequestMapping("/als/order-change-teacher")
@Validated
public class OrderChangeTeacherController {

    @Resource
    private OrderChangeTeacherService orderChangeTeacherService;
    
    @Resource
    private ChangeTeacherFacade changeTeacherFacade;
    

    @PostMapping("/create")
    @Operation(summary = "创建更换老师")
    @PreAuthorize("@ss.hasPermission('als:order-change-teacher:create')")
    public CommonResult<Long> createOrderChangeTeacher(@Valid @RequestBody OrderChangeTeacherSaveReqVO createReqVO) {
        return success(orderChangeTeacherService.createOrderChangeTeacher(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新更换老师")
    @PreAuthorize("@ss.hasPermission('als:order-change-teacher:update')")
    public CommonResult<Boolean> updateOrderChangeTeacher(@Valid @RequestBody OrderChangeTeacherSaveReqVO updateReqVO) {
        orderChangeTeacherService.updateOrderChangeTeacher(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除更换老师")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:order-change-teacher:delete')")
    public CommonResult<Boolean> deleteOrderChangeTeacher(@RequestParam("id") Long id) {
        orderChangeTeacherService.deleteOrderChangeTeacher(id);
        return success(true);
    }

    @PutMapping("/audit")
    @Operation(summary = "审核")
    @PreAuthorize("@ss.hasPermission('als:order-change-teacher:update')")
    public CommonResult<Boolean> auditOrderChangeTeacher(@Valid @RequestBody AuditVo auditVo) {
        changeTeacherFacade.auditOrderChangeTeacher(auditVo);
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得更换老师")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:order-change-teacher:query')")
    public CommonResult<OrderChangeTeacherRespVO> getOrderChangeTeacher(@RequestParam("id") Long id) {
        OrderChangeTeacherDO orderChangeTeacher = orderChangeTeacherService.getOrderChangeTeacher(id);
        return success(BeanUtils.toBean(orderChangeTeacher, OrderChangeTeacherRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得更换老师分页")
    @PreAuthorize("@ss.hasPermission('als:order-change-teacher:query')")
    public CommonResult<PageResult<OrderChangeTeacherRespVO>> getOrderChangeTeacherPage(@Valid OrderChangeTeacherPageReqVO pageReqVO) {
        PageResult<OrderChangeTeacherDO> pageResult = orderChangeTeacherService.getOrderChangeTeacherPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, OrderChangeTeacherRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出更换老师 Excel")
    @PreAuthorize("@ss.hasPermission('als:order-change-teacher:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportOrderChangeTeacherExcel(@Valid OrderChangeTeacherPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<OrderChangeTeacherDO> list = orderChangeTeacherService.getOrderChangeTeacherPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "更换老师.xls", "数据", OrderChangeTeacherRespVO.class,
                        BeanUtils.toBean(list, OrderChangeTeacherRespVO.class));
    }

}
