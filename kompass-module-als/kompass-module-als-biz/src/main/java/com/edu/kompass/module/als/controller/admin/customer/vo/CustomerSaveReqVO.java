package com.edu.kompass.module.als.controller.admin.customer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 家长新增/修改 Request VO")
@Data
public class CustomerSaveReqVO {

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "2115")
    private Long customerId;

    @Schema(description = "家长姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "家长姓名不能为空")
    private String customerName;

    @Schema(description = "家长性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "家长性别不能为空")
    private Integer customerSex;

    @Schema(description = "孩子关系", requiredMode = Schema.RequiredMode.REQUIRED, example = "爸爸")
    @NotEmpty(message = "孩子关系不能为空")
    private String relationship;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "手机号不能为空")
    private String customerPhone;

    @Schema(description = "课时剩余", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private BigDecimal lessonPeriodRemain;

    @Schema(description = "openId", requiredMode = Schema.RequiredMode.REQUIRED, example = "6313")
    private String openId;

    @Schema(description = "服务状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "[1,2]")
    @NotNull(message = "服务状态不能为空")
    private Integer serviceStatus;

    @Schema(description = "获客渠道", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "获客渠道不能为空")
    private Integer sourceChannel;

    @Schema(description = "服务标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "服务标签不能为空")
    private List<Integer> serviceTags;

    @Schema(description = "运营标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "运营标签不能为空")
    private List<Integer> operationTags;

    @Schema(description = "分级标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "分级标签不能为空")
    private List<Integer> levelTags;

    @Schema(description = "注册时间")
    private LocalDateTime registerTime;

    @Schema(description = "最近登录时间")
    private LocalDateTime lastLoginTime;

    @Schema(description = "运营负责人", requiredMode = Schema.RequiredMode.REQUIRED, example = "7453")
    @NotNull(message = "运营负责人不能为空")
    private Long headOperateUserId;

    @Schema(description = "市场负责人", requiredMode = Schema.RequiredMode.REQUIRED, example = "9044")
    @NotNull(message = "市场负责人不能为空")
    private Long headMarketUserId;

    @Schema(description = "家长备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @NotEmpty(message = "家长备注不能为空")
    private String customerRemark;

}
