package com.edu.kompass.module.als.controller.admin.coursepackage.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 课时包分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CoursePackagePageReqVO extends PageParam {

    @Schema(description = "课时包ID", example = "25891")
    private Long coursePackageId;

    @Schema(description = "课时包名称", example = "王五")
    private String packageName;

    @Schema(description = "课时包类型", example = "1")
    private Integer packageType;

    @Schema(description = "总课时数", example = "60")
    private Integer lessonPeriod;

    @Schema(description = "售价", example = "11312")
    private BigDecimal salePrice;

    @Schema(description = "是否启用", example = "1")
    private Integer isEnable;

}
