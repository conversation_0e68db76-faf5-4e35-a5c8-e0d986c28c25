package com.edu.kompass.module.als.controller.admin.orderconfirm.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 接单确认分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderConfirmPageReqVO extends PageParam {

    @Schema(description = "主键", example = "31244")
    private Long orderConfirmId;

    @Schema(description = "订单ID", example = "4414")
    private Long orderId;

    @Schema(description = "家长ID", example = "25149")
    private Long customerId;

    @Schema(description = "家长姓名", example = "王五")
    private String customerName;

    @Schema(description = "老师ID", example = "11844")
    private Long teacherId;

    @Schema(description = "老师姓名", example = "王五")
    private String teacherName;

    @Schema(description = "处理状态", example = "1")
    private Integer dealStatus;

    @Schema(description = "拒绝原因ID", example = "1")
    private Integer rejectReasonId;

    @Schema(description = "自定义不合适原因", example = "不好")
    private String customReason;

    @Schema(description = "处理时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] dealTime;

    @Schema(description = "处理人", example = "31127")
    private Long dealUserId;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
