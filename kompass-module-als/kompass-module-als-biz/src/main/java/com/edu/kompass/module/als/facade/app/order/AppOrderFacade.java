package com.edu.kompass.module.als.facade.app.order;

import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.module.als.controller.app.customer.vo.AppCustomerReqVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppOrderPageReqVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppOrderRespVO;
import com.edu.kompass.module.als.controller.app.customer.vo.AppTeacherFavRespVO;
import com.edu.kompass.module.als.controller.app.teacher.vo.AppTeacherReqVO;
import com.edu.kompass.module.als.enums.FavTypeEnum;

public interface AppOrderFacade {

    PageResult<AppOrderRespVO> getTeacherOrderPage(AppOrderPageReqVO pageReqVO);
    PageResult<AppOrderRespVO> getCustomerOrderPage(AppOrderPageReqVO pageReqVO);

    AppOrderRespVO getOrder(AppCustomerReqVO reqVO);
    AppOrderRespVO getOrder(AppTeacherReqVO reqVO);
    /**
     * 接单大厅
     * @param pageReqVO
     * @return
     */
    PageResult<AppOrderRespVO> getOrderCenterPage(AppOrderPageReqVO pageReqVO);

    AppOrderRespVO getOrderCenterDetail(Long orderId,Long teacherMemberId);

    /**
     * 收藏
     * @param id
     * @param teacherMemberId
     * @return
     */
    AppTeacherFavRespVO fav(Long id, Long teacherMemberId, FavTypeEnum favType);

    /**
     * 取消收藏
     * @param orderId
     * @param teacherMemberId
     * @return
     */
    void cancelFav(Long orderId,Long teacherMemberId);

    /**
     * 申请接单
     * @param orderId
     * @param teacherId
     * @return
     */
    Long applyOrder(Long orderId,Long teacherId);

    /**
     * 我的收藏
     * @param pageReqVO
     * @return
     */
    PageResult<AppOrderRespVO> getMyFav(AppOrderPageReqVO pageReqVO);
}
