package com.edu.kompass.module.als.controller.admin.lessonschedule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 时间规划执行新增/修改 Request VO")
@Data
public class LessonScheduleSaveReqVO {

    @Schema(description = "时间规划执行ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24106")
    private Long lessonScheduleId;

    @Schema(description = "陪学记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20091")
    @NotNull(message = "陪学记录ID不能为空")
    private Long lessonRecordId;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "间隔：分钟", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "间隔：分钟不能为空")
    private Integer periodMinute;

    @Schema(description = "实际结束时间")
    private LocalDateTime realEndTime;

    @Schema(description = "实际结束时间误差：分钟", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "实际结束时间误差：分钟不能为空")
    private Long errorMinute;

    @Schema(description = "任务名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "任务名称不能为空")
    private String taskContent;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态不能为空")
    private Integer taskStatus;

    @Schema(description = "原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不喜欢")
    @NotEmpty(message = "原因不能为空")
    private String reason;

    @Schema(description = "方案", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "方案不能为空")
    private String programme;

}
