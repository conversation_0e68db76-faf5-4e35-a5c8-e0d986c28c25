package com.edu.kompass.module.als.controller.admin.lessonhour.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 课时记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LessonHourPageReqVO extends PageParam {

    @Schema(description = "陪学记录ID", example = "17973")
    private Long lessonRecordId;

    @Schema(description = "购买记录ID", example = "6746")
    private Long customerPackageId;

    @Schema(description = "上课类型：1课后陪读 2半天伴读 3全天伴读 4钢琴", example = "1")
    private Integer lessonType;

    @Schema(description = "人数：1一对一 2一对多")
    private Integer childNumber;

    @Schema(description = "家长ID", example = "26023")
    private Long customerId;

    @Schema(description = "老师ID", example = "18478")
    private Long teacherId;

    @Schema(description = "上课时长")
    private BigDecimal timeHour;

    @Schema(description = "倍数")
    private BigDecimal multiple;

    @Schema(description = "课时数（时长/标准时长）")
    private BigDecimal lessonHour;

    @Schema(description = "总课时(课时数*倍数）")
    private BigDecimal classHour;

    @Schema(description = "老师-课时单价（基础单价*课时标准）", example = "2987")
    private BigDecimal teacherPrice;

    @Schema(description = "老师-小时单价(课时单价/课时标准)", example = "1451")
    private BigDecimal teacherHourPrice;

    @Schema(description = "老师-附加总费用")
    private BigDecimal teacherExtraCharge;

    @Schema(description = "老师-总薪资")
    private BigDecimal teacherAmount;

    @Schema(description = "家长-课时单价", example = "14629")
    private BigDecimal customerPrice;

    @Schema(description = "家长-消费金额")
    private BigDecimal customerCost;

    @Schema(description = "下次上课时间 yyyy-mm-dd hh:mm")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] nextTime;

    @Schema(description = "记录状态 0未确认 1已确认 2已取消 3押金 4押金已处理", example = "2")
    private Integer recordStatus;

    @Schema(description = "审核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] auditTime;

    @Schema(description = "审核状态 1审核中 2审核通过 3驳回", example = "2")
    private Integer auditStatus;

    @Schema(description = "审核人", example = "23337")
    private Long auditUserId;

    @Schema(description = "审核备注", example = "你说的对")
    private String auditRemark;

    @Schema(description = "备注时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] remarkTime;

    @Schema(description = "备注人", example = "3286")
    private Long remarkUserId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
