package com.edu.kompass.module.als.controller.admin.customeraddapply;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.audit.AuditVo;
import com.edu.kompass.module.als.controller.admin.customeraddapply.vo.CustomerAddApplyPageReqVO;
import com.edu.kompass.module.als.controller.admin.customeraddapply.vo.CustomerAddApplyRespVO;
import com.edu.kompass.module.als.controller.admin.customeraddapply.vo.CustomerAddApplySaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.customeraddapply.CustomerAddApplyDO;
import com.edu.kompass.module.als.facade.customer.CustomerFacade;
import com.edu.kompass.module.als.service.customeraddapply.CustomerAddApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 课时添加申请")
@RestController
@RequestMapping("/als/customer-add-apply")
@Validated
public class CustomerAddApplyController {

    @Resource
    private CustomerAddApplyService customerAddApplyService;

    @Autowired
    private CustomerFacade customerFacade;

    @PostMapping("/create")
    @Operation(summary = "创建课时添加申请")
    @PreAuthorize("@ss.hasPermission('als:customer-add-apply:create')")
    public CommonResult<Long> createCustomerAddApply(@Valid @RequestBody CustomerAddApplySaveReqVO createReqVO) {
        return success(customerFacade.createCustomerAddApply(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新课时添加申请")
    @PreAuthorize("@ss.hasPermission('als:customer-add-apply:update')")
    public CommonResult<Boolean> updateCustomerAddApply(@Valid @RequestBody CustomerAddApplySaveReqVO updateReqVO) {
        customerAddApplyService.updateCustomerAddApply(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除课时添加申请")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:customer-add-apply:delete')")
    public CommonResult<Boolean> deleteCustomerAddApply(@RequestParam("id") Long id) {
        customerAddApplyService.deleteCustomerAddApply(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得课时添加申请")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:customer-add-apply:query')")
    public CommonResult<CustomerAddApplyRespVO> getCustomerAddApply(@RequestParam("id") Long id) {
        CustomerAddApplyDO customerAddApply = customerAddApplyService.getCustomerAddApply(id);
        return success(BeanUtils.toBean(customerAddApply, CustomerAddApplyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "课时添加申请-分页")
    @PreAuthorize("@ss.hasPermission('als:customer-add-apply:query')")
    public CommonResult<PageResult<CustomerAddApplyRespVO>> getCustomerAddApplyPage(@Valid CustomerAddApplyPageReqVO pageReqVO) {
        PageResult<CustomerAddApplyRespVO> result = customerFacade.getCustomerAddApplyPage(pageReqVO);
        return success(result);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出课时添加申请 Excel")
    @PreAuthorize("@ss.hasPermission('als:customer-add-apply:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCustomerAddApplyExcel(@Valid CustomerAddApplyPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CustomerAddApplyDO> list = customerAddApplyService.getCustomerAddApplyPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "课时添加申请.xls", "数据", CustomerAddApplyRespVO.class,
                        BeanUtils.toBean(list, CustomerAddApplyRespVO.class));
    }

    @PutMapping("/audit")
    @Operation(summary = "审核")
    @PreAuthorize("@ss.hasPermission('als:customer-add-apply:update')")
    public CommonResult<Boolean> audit(@Valid @RequestBody AuditVo auditVo) {
        customerFacade.customerPackageAudit(auditVo);
        return success(true);
    }

}
