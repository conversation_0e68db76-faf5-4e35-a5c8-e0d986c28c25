package com.edu.kompass.module.als.controller.admin.lessonschedule;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.lessonschedule.vo.LessonSchedulePageReqVO;
import com.edu.kompass.module.als.controller.admin.lessonschedule.vo.LessonScheduleRespVO;
import com.edu.kompass.module.als.controller.admin.lessonschedule.vo.LessonScheduleSaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.lessonschedule.LessonScheduleDO;
import com.edu.kompass.module.als.service.lessonschedule.LessonScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 时间规划执行")
@RestController
@RequestMapping("/als/lesson-schedule")
@Validated
public class LessonScheduleController {

    @Resource
    private LessonScheduleService lessonScheduleService;

    @PostMapping("/create")
    @Operation(summary = "创建时间规划执行")
    @PreAuthorize("@ss.hasPermission('als:lesson-schedule:create')")
    public CommonResult<Long> createLessonSchedule(@Valid @RequestBody LessonScheduleSaveReqVO createReqVO) {
        return success(lessonScheduleService.createLessonSchedule(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新时间规划执行")
    @PreAuthorize("@ss.hasPermission('als:lesson-schedule:update')")
    public CommonResult<Boolean> updateLessonSchedule(@Valid @RequestBody LessonScheduleSaveReqVO updateReqVO) {
        lessonScheduleService.updateLessonSchedule(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除时间规划执行")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:lesson-schedule:delete')")
    public CommonResult<Boolean> deleteLessonSchedule(@RequestParam("id") Long id) {
        lessonScheduleService.deleteLessonSchedule(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得时间规划执行")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:lesson-schedule:query')")
    public CommonResult<LessonScheduleRespVO> getLessonSchedule(@RequestParam("id") Long id) {
        LessonScheduleDO lessonSchedule = lessonScheduleService.getLessonSchedule(id);
        return success(BeanUtils.toBean(lessonSchedule, LessonScheduleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得时间规划执行分页")
    @PreAuthorize("@ss.hasPermission('als:lesson-schedule:query')")
    public CommonResult<PageResult<LessonScheduleRespVO>> getLessonSchedulePage(@Valid LessonSchedulePageReqVO pageReqVO) {
        PageResult<LessonScheduleDO> pageResult = lessonScheduleService.getLessonSchedulePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, LessonScheduleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出时间规划执行 Excel")
    @PreAuthorize("@ss.hasPermission('als:lesson-schedule:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportLessonScheduleExcel(@Valid LessonSchedulePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LessonScheduleDO> list = lessonScheduleService.getLessonSchedulePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "时间规划执行.xls", "数据", LessonScheduleRespVO.class,
                        BeanUtils.toBean(list, LessonScheduleRespVO.class));
    }

}
