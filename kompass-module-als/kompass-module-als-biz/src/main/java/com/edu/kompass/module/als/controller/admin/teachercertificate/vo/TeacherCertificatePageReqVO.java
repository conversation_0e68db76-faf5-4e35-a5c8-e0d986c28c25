package com.edu.kompass.module.als.controller.admin.teachercertificate.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 老师证书分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeacherCertificatePageReqVO extends PageParam {

    @Schema(description = "老师ID", example = "2144")
    private Long teacherId;

    @Schema(description = "老师姓名", example = "赵六")
    private String teacherName;

    @Schema(description = "性别")
    private String teacherSex;

    @Schema(description = "身份证", requiredMode = Schema.RequiredMode.REQUIRED)
    private String teacherIdNumber;

    @Schema(description = "证书照片URL", example = "https://www.iocoder.cn")
    private String picUrl;

    @Schema(description = "证书编号")
    private String certificateNo;

    @Schema(description = "证书状态 0有效 1无效-已过期 2无效-已注销", example = "2")
    private Integer certificateStatus;

    @Schema(description = "有效期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] validTime;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
