package com.edu.kompass.module.als.controller.admin.faq.vo;

import com.edu.kompass.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static com.edu.kompass.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 常见问题解答分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class FaqPageReqVO extends PageParam {

    @Schema(description = "可见方")
    private Integer faqWho;

    @Schema(description = "问题分类", example = "1")
    private Integer faqType;

    @Schema(description = "问题")
    private String faqQuestion;

    @Schema(description = "解答")
    private String faqAnswer;

    @Schema(description = "状态", example = "1")
    private Integer faqStatus;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}
