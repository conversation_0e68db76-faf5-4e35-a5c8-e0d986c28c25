package com.edu.kompass.module.als.controller.admin.depositrefundapply.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 退押申请处理 Request VO")
@Data
public class DepositRefundDealReqVO {

    @Schema(description = "课时记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23575")
    @NotNull(message = "课时记录ID不能为空")
    private Long lessonHourId;

    @Schema(description = "处理方式", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "处理方式不能为空")
    private Integer dealMethod;

    @Schema(description = "家长分得", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.00")
    private BigDecimal customerGet;

    @Schema(description = "老师分得", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.00")
    private BigDecimal teacherGet;

    @Schema(description = "家长分得", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.00")
    private BigDecimal platformGet;

    private Long loginUserId;
}
