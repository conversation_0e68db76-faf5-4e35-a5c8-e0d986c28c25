package com.edu.kompass.module.als.controller.admin.teacherability;

import com.edu.kompass.framework.apilog.core.annotation.ApiAccessLog;
import com.edu.kompass.framework.common.pojo.CommonResult;
import com.edu.kompass.framework.common.pojo.PageParam;
import com.edu.kompass.framework.common.pojo.PageResult;
import com.edu.kompass.framework.common.util.object.BeanUtils;
import com.edu.kompass.framework.excel.core.util.ExcelUtils;
import com.edu.kompass.module.als.controller.admin.teacherability.vo.TeacherAbilityPageReqVO;
import com.edu.kompass.module.als.controller.admin.teacherability.vo.TeacherAbilityRespVO;
import com.edu.kompass.module.als.controller.admin.teacherability.vo.TeacherAbilitySaveReqVO;
import com.edu.kompass.module.als.dal.dataobject.teacherability.TeacherAbilityDO;
import com.edu.kompass.module.als.facade.teacher.TeacherAbilityFacade;
import com.edu.kompass.module.als.service.teacherability.TeacherAbilityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

import static com.edu.kompass.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static com.edu.kompass.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 老师能力")
@RestController
@RequestMapping("/als/teacher-ability")
@Validated
public class TeacherAbilityController {

    @Resource
    private TeacherAbilityService teacherAbilityService;

    @Resource
    private TeacherAbilityFacade teacherAbilityFacade;

    @PostMapping("/create")
    @Operation(summary = "创建老师能力")
    @PreAuthorize("@ss.hasPermission('als:teacher-ability:create')")
    public CommonResult<Long> createTeacherAbility(@Valid @RequestBody TeacherAbilitySaveReqVO createReqVO) {
        return success(teacherAbilityService.createTeacherAbility(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新老师能力")
    @PreAuthorize("@ss.hasPermission('als:teacher-ability:update')")
    public CommonResult<Boolean> updateTeacherAbility(@Valid @RequestBody TeacherAbilitySaveReqVO updateReqVO) {
        teacherAbilityService.updateTeacherAbility(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除老师能力")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('als:teacher-ability:delete')")
    public CommonResult<Boolean> deleteTeacherAbility(@RequestParam("id") Long id) {
        teacherAbilityService.deleteTeacherAbility(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得老师能力")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('als:teacher-ability:query')")
    public CommonResult<TeacherAbilityRespVO> getTeacherAbility(@RequestParam("id") Long id) {
        TeacherAbilityDO teacherAbility = teacherAbilityService.getTeacherAbility(id);
        return success(BeanUtils.toBean(teacherAbility, TeacherAbilityRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得老师能力分页")
    @PreAuthorize("@ss.hasPermission('als:teacher-ability:query')")
    public CommonResult<PageResult<TeacherAbilityRespVO>> getTeacherAbilityPage(@Valid TeacherAbilityPageReqVO pageReqVO) {
        PageResult<TeacherAbilityRespVO> teacherAbilityPage = teacherAbilityFacade.getTeacherAbilityPage(pageReqVO);
        return success(teacherAbilityPage);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出老师能力 Excel")
    @PreAuthorize("@ss.hasPermission('als:teacher-ability:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTeacherAbilityExcel(@Valid TeacherAbilityPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TeacherAbilityDO> list = teacherAbilityService.getTeacherAbilityPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "老师能力.xls", "数据", TeacherAbilityRespVO.class,
                        BeanUtils.toBean(list, TeacherAbilityRespVO.class));
    }

}
