package com.edu.kompass.module.als.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 陪学订单新增/修改 Request VO")
@Data
public class OrderSaveReqVO {

    @Schema(description = "主键", example = "1395")
    private Long orderId;

    @Schema(description = "订单编号", example = "PX20240903")
    private String orderNo;

    @Schema(description = "订单类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "订单类型不能为空")
    private Integer orderType;

    @Schema(description = "课程类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "课程类型不能为空")
    private Integer lessonType;

    @Schema(description = "家长ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "27437")
    @NotNull(message = "家长ID不能为空")
    private Long customerId;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "17302589292")
    @NotEmpty(message = "手机号不能为空")
    private String customerPhone;

    @Schema(description = "进程", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer orderProcess;

    @Schema(description = "区域", requiredMode = Schema.RequiredMode.REQUIRED, example = "784")
    @NotNull(message = "区域不能为空")
    private Integer orderAreaId;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "江苏省南京市XX小区")
    @NotEmpty(message = "详细地址不能为空")
    private String orderAddress;

    @Schema(description = "是否暂停接单", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "是否暂停接单不能为空")
    private Integer isSuspend;

    @Schema(description = "是否已建群", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @NotNull(message = "是否已建群不能为空")
    private Integer isGroupChat;

    @Schema(description = "跟踪备注补充", example = "XXXX")
    private String orderRemarkExtra;

    @Schema(description = "订单备注", example = "这个家长很相信我们")
    private String orderRemark;

    @Schema(description = "当前负责人", example = "1")
    private Integer headCurrent;

    @Schema(description = "运营负责人", example = "1")
    private Integer headOperate;

    @Schema(description = "市场负责人", example = "1")
    private Integer headMarket;

    @Schema(description = "原家长陪学需求", example = "【到家陪学】")
    private String orgNeedsContent;

    @Schema(description = "老师性别要求", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "老师性别要求不能为空")
    private Integer requireSex;

    @Schema(description = "老师能力硬性要求", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "老师能力硬性要求不能为空")
    private List<String> hardRequireAbility;

    @Schema(description = "老师能力要求补充", example = "要求教育专业")
    private String requireAbilityExtra;

    @Schema(description = "需求标签", requiredMode = Schema.RequiredMode.REQUIRED, example = "1,2")
    @NotEmpty(message = "需求标签不能为空")
    private List<String> needsTags;

    @Schema(description = "需求侧重点", example = "1,2")
    private List<String> needsFocusTags;

    @Schema(description = "陪学要求", requiredMode = Schema.RequiredMode.REQUIRED, example = "【到家陪学】")
    @NotEmpty(message = "陪学要求不能为空")
    private String demandContent;

    @Schema(description = "周次", example = "1")
    private Integer timesWeek;

    @Schema(description = "是否是周末订单", example = "1")
    private Integer isOnWeekend;

    @Schema(description = "陪学时间范围", requiredMode = Schema.RequiredMode.REQUIRED, example = "1,2")
    @NotEmpty(message = "陪学时间范围不能为空")
    private List<Integer> timeRange;

    @Schema(description = "跟踪时间", example = "2024-07-30 22:55:45")
    private LocalDateTime trackingTime;
    
    @Schema(description = "实际跟踪时间", example = "2024-07-30 22:55:45")
    private LocalDateTime actualTrackingTime;

    @Schema(description = "跟踪备注标签", example = "[1,2,3]")
    private List<Integer> trackingRemarkTags;

    @Schema(description = "体验时间", example = "2024-07-30 22:55:59")
    private LocalDateTime expTime;

    @Schema(description = "是否确认体验时间", example = "1")
    private Integer isConfirmExpTime;

    @Schema(description = "孩子性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "孩子性别不能为空")
    private Integer kidSex;

    @Schema(description = "孩子年级阶段", requiredMode = Schema.RequiredMode.REQUIRED, example = "12")
    @NotNull(message = "孩子年级阶段不能为空")
    private Integer kidStage;

    @Schema(description = "沟通前提条件", example = "1,2")
    private List<String> communicatePre;

    @Schema(description = "沟通结果", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "沟通结果不能为空")
    private Integer communicateResult;

    @Schema(description = "为什么需要陪学", example = "家里需要")
    private String whyNeed;

    @Schema(description = "主要教育者", example = "妈妈")
    private String primaryEducator;

    @Schema(description = "孩子称呼", requiredMode = Schema.RequiredMode.REQUIRED, example = "李娃")
    @NotEmpty(message = "孩子称呼不能为空")
    private String kidNickName;

    @Schema(description = "家长与孩子关系", requiredMode = Schema.RequiredMode.REQUIRED, example = "母子")
    @NotEmpty(message = "家长与孩子关系不能为空")
    private String relationship;

    @Schema(description = "学校名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String schoolName;

    @Schema(description = "学校性质", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "学校性质不能为空")
    private Integer schoolNature;

    @Schema(description = "大致排名", example = "1")
    private Integer ranking;

    @Schema(description = "孩子性格", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "孩子性格不能为空")
    private String kidChr;

    @Schema(description = "孩子性格补充", example = "无")
    private String kidChrExtra;

    @Schema(description = "孩子兴趣", example = "1")
    private String kidInt;

    @Schema(description = "孩子兴趣补充", example = "无")
    private String kidIntExtra;

    @Schema(description = "邀请人", example = "56")
    private Integer inviterId;
    
    @Schema(description = "匹配老师", example = "1")
    private Long matchTeacherId;

    @Schema(description = "订单来源渠道", example = "1")
    private Integer sourceChannel;

    @Schema(description = "订单状态", example = "1")
    private Integer orderStatus;

    @Schema(description = "审核人", example = "1")
    private Long auditUserId;

    @Schema(description = "审核时间", example = "2024-07-30 22:55:59")
    private LocalDateTime auditTime;

    @Schema(description = "发布状态", example = "1")
    private Integer releaseStatus;

    @Schema(description = "发布时间", example = "2024-07-30 22:55:59")
    private LocalDateTime releaseTime;

    @Schema(description = "发布人", example = "1")
    private Long releaseUserId;
    
    @Schema(description = "注册下单时间", example = "2024-07-30 22:55:59")
    private LocalDateTime registerTime;

    @Schema(description = "经度")
    private String longitude;

    @Schema(description = "纬度")
    private String latitude;
    
    @Schema(description = "解析地址")
    private String parseAddress;
}
