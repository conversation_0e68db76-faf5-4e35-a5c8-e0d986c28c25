<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.edu.kk</groupId>
        <artifactId>kompass-module-als</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>kompass-module-als-biz</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>

    <dependencies>  <!-- 5. 新增依赖，这里引入的都是比较常用的业务组件、技术组件 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-als-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>
         <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-infra-biz</artifactId>
             <version>${revision}</version>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-biz-ip</artifactId>
        </dependency>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-pay-biz</artifactId>
            <version>2.3.0-jdk8-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-trade-api</artifactId>
            <version>2.3.0-jdk8-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-product-api</artifactId>
            <version>2.3.0-jdk8-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-module-member-api</artifactId>
            <version>2.3.0-jdk8-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.edu.kk</groupId>
            <artifactId>kompass-spring-boot-starter-protection</artifactId>
        </dependency>

    </dependencies>
</project>
